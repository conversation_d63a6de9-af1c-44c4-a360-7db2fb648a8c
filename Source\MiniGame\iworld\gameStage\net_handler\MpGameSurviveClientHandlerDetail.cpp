

#include "MpGameSuviveNetHandler.h"
#include "GameNetManager.h"
#include "SandBoxManager.h"
#include "world_types.h"
#include "chunk.h"
#include "world.h"
#include "WorldManager.h"
#include "GameMode.h"
#include "BlockMaterialMgr.h"
#include "GameEvent.h"
#include "VehicleWorld.h"
#include "ActorCSProto.h"
#include "backpack.h"
#include "DefManagerProxy.h"
#include "ClientGameManager.h"
#include "LuaInterfaceProxy.h"
#include "ClientPlayer.h"
#include "MpPlayerControl.h"
#include "ClientActorManager.h"
#include "ClientItem.h"
#include "Environment.h"
#include "ClientInfoProxy.h"
#include "MpActorManager.h"
#include "GameEffectManager.h"
#include "ActorBody.h"
#include "ClientMob.h"
#include "ActorBoat.h"
#include "container_world.h"
#include "ActorMechaUnit.h"
#include "component/GunUseComponent.h"
#include "component/CustomGunUseComponent.h"
#include "ActorTrainCar.h"
#include "GameCamera.h"
#include "ActorRocket.h"
#include "ActorHorse.h"
#include "ActorDragonMount.h"
#include "ClientActorAttract.h"
#include "PlayerTaskManager.h"

#include "container_regionreplicator.h"
#include "container_buildblueprint.h"
#include "container_fullycustommodel.h"
#include "CustomModelMgr.h"
#include "FullyCustomModelMgr.h"
#include "ActorVehicleAssemble.h"
#include "VehicleAssembleLocoMotion.h"
#include "RoomSyncResMgr.h"
#include "PackingFullyCustomModelMgr.h"
#include "special_blockid.h"
#include "SandboxIdDef.h"
#include "DynamicLightingManager.h"
#include "ResourceCenter.h"

#include "File/DirVisitor.h"
#include "OgreScriptLuaVM.h"
#include "Platforms/PlatformInterface.h"
#include "Entity/OgreEntity.h"
#include "LegacyCompress.h"
#include "OgreMD5.h"
#include "chunkio.h"
#include <type_traits>
#include <cstdio>
#include "ActorBasketBall.h"
#include "ActorShapeShiftHorse.h"
#include "ActorSandworm.h"
#include "container_sensor.h"
#include "VehicleContainerActioner.h"
#include "ActorVillager.h"
#include "ImportCustomModelMgr.h"
#include "ActorMoonMount.h"
#include "OgreTimer.h"
#include "ActorDouDuMount.h"
#include "ActorPumpkinHorse.h"

#include "container_decomposition.h"
#include "SocRevivePointComponent.h"
#include "container_pot.h"
#include "StarStationTransferMgr.h"
#include "container_starstationtransfercabin.h"
#include "OgreScriptLuaVM.h"
#include "ActorDragon.h"
#include "ActorGiant.h"
#include "ClientVacantBoss.h"
#include "world.h"
#include "ChunkGen_Normal.h"
#include "RoomClient.h"
#include "BlockMeasureDistance.h"
#include "SandboxEventDispatcherManager.h"
#include "LightningChainComponent.h"
#include "FishingComponent.h"

#include "WorldRender.h"
#include "SkyPlane.h"

#include "SandboxObject.h"
#include "SandboxCoreDriver.h"
#include "SandboxEventDispatcherManager.h"
#include "container_world_lua.h"
#include "PlayerAttrib.h"
#include "IRecordInterface.h"

#include "MpGameSurvive.h"
#include "ClientErrCode.h"

#include "SprayPaintMgr.h"		//20211020 导入喷漆头文件 codeby:柯冠强
#include "SandboxGame/Mgr/GunManager/BulletMgr.h"
#include "File/FileManager.h"
#include "AssetPipeline/AssetManager.h"
#include "GameInfoProxy.h"
#include "Sound/MusicManager.h"
#include "RiddenComponent.h"
#include "EffectComponent.h"
#include "TeamComponent.h"
#include "SoundComponent.h"
#include "ActionAttrStateComponent.h"
#include "BindActorComponent.h"
#include "PlotComponent.h"
#include "ActorInPortal.h"
#include "ActorBindVehicle.h"
#include "ExploitState.h"
#include "AdvancedDigState.h"
#include "FootballStateAction.h"
#include "BasketballStateAction.h"
#include "PushSnowBallStateAction.h"
#include "DigState.h"
#include "SleepState.h"
#include "ActorSandworm.h"
#include "PermitsSubSystem.h"

#include "SandboxReplicatorRoot.h"
#include "StatisticsManagerProxy.h"
#include "StatisticsActionId.h"
#include "CommonUtil.h"
#include "SandboxGlobalNotify.h"
#include "ThornBallComponent.h"
#include "Optick/optick.h"
#include "ActorPushSnowBall.h"
#include "Misc/FrameTimeManager.h"
#include "CustomModel.h"
#include "EffectParticle.h"
#include "SandboxGame/Components/Base/PhysicsComponent.h"
#include "SandboxGame/Components/Base/ModelComponent.h"
#include "SandboxChatService.h"
#include "IMiniDeveloperProxy.h"
#include "PlayerControl.h"
#include "backpack/CraftingQueue.h"
#include "PlayerDownedStateAttrib.h"

#include "SandboxGame.h"
#include "SandboxDriverModule.h"
#include "ModPackMgr.h"
#include "GameAnalytics.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;
static bool s_ReceieveFirstChunk = false;

EXPORT_SANDBOXGAME extern ClientActor* CreateActorFromGeneralEnterAOIHC(const game::hc::PB_GeneralEnterAOIHC& pb);
namespace MNSandbox {
	namespace MiniReport {
		EXPORT_SANDBOXDRIVERMODULE extern void StudioReportSandbox(int loadingstage, int substage, bool isfinish);
	}
}
void MpGameSurviveNetHandler::handleCustomMsg2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_Custom_Msg msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	char* name = (char*)msg.msgname().c_str();
	char* content = (char*)msg.content().c_str();
	int ziplen = msg.ziplen();
	int unziplen = msg.unziplen();
	int compresstype = Rainbow::CompressTool::COMPRESS_LZMA;
	Rainbow::CompressTool ctool(compresstype);
	//size_t destlen = ctool.compressBound(obj.binLen());
	SDB_NETMONITOR_RECV_CUSTOM((unsigned)(pkg.ByteSize + PB_PROTO_HEAD_LEN + 9), name, 0)

	if (unziplen)
	{
		static char afterDatabuf[2048];
		char *destdata = NULL;
		if (unziplen <= 2048)
		{
			destdata = afterDatabuf;
		}
		else
		{
			destdata = (char *)malloc(unziplen);
		}
		if (ctool.decompress(destdata, unziplen, content, ziplen))
		{
			//jsonxx::Object obj;
			//obj.parseBinary((unsigned char*)destdata, unziplen);
			//GetSandBoxManager().doEventEx(name, &obj);
			GetSandBoxManager().doEventExBin(name, 0, destdata, unziplen);
		}
		if (destdata != afterDatabuf)
		{
			free(destdata);
		}
	}
	else
	{
		//WarningStringMsg("[crash_info] recv_msg name:%s, len:%d", name, ziplen);
		GetSandBoxManager().doEventExBin(name, 0, content, ziplen);
	}
}


void MpGameSurviveNetHandler::onClientConnectSuccess()
{
	ENTRYMAPCOST_STEP("MpGameSurviveNetHandler::onClientConnectSuccess");
	// 2021-12-14 codeby:liusijia 云服等服务器下发一次extradata，确保房间信息正确
	auto hostType = GetGameInfoProxy()->GetRoomHostType();
	m_root->setCurLadingState(hostType == ROOM_SERVER_RENT ? CLOAD_CHECK_INFO : CLOAD_INIT_VM);

	MpGameSurvive* mp = dynamic_cast<MpGameSurvive*>(m_root);
	if (mp)
	{
		mp->clearDownloadList();
	}
	GetClientInfoProxy()->setCurRoomOwner(GameNetManager::getInstance()->getHostUin());

#ifndef IWORLD_SERVER_BUILD
	// 客户端连接房间成功埋点
	GameAnalytics::TrackEvent("player_room_connect_success", {
		{"uin", GameAnalytics::Value(GetClientInfoProxy()->getUin())},
		{"host_uin", GameAnalytics::Value(GameNetManager::getInstance()->getHostUin())},
		{"room_host_type", GameAnalytics::Value(hostType)},
	});
#endif

	GameNetClientMsgHandler::onClientConnectSuccess();
}

void MpGameSurviveNetHandler::onClientConnectFailed(int errcode)
{
	// 2022-02-09 codeby:liusijia 新增加载失败上报
	MINIW::ScriptVM::game()->callFunction("MpGameLoadErrReport", "i", errcode);
	m_root->setCurLadingState(-1 * abs(errcode));
	terminateMPGame(errcode);

	GameNetClientMsgHandler::onClientConnectFailed(errcode);
}

void MpGameSurviveNetHandler::onClientAuthenFailed()
{
	m_root->setCurLadingState(-1 * abs(ERR_CODE_AUTHEN_FAIL));
	terminateMPGame(ERR_CODE_AUTHEN_FAIL);
}

void MpGameSurviveNetHandler::onClientAddPartner(int uin)
{
}

void MpGameSurviveNetHandler::onClientConnectionLost()
{

	LOG_INFO("GAMENET:MpGameSurviveNetHandler::onClientConnectionLost");
	m_root->setCurLadingState(-1 * abs(ERR_CODE_CLIENT_LOST_CONN));

#ifndef IWORLD_SERVER_BUILD
	// 客户端连接丢失埋点
	GameAnalytics::TrackEvent("player_connection_lost", {
		{"uin", GameAnalytics::Value(GetClientInfoProxy()->getUin())},
		{"host_uin", GameAnalytics::Value(GameNetManager::getInstance()->getHostUin())},
		{"error_code", GameAnalytics::Value(ERR_CODE_CLIENT_LOST_CONN)},
		{"room_host_type", GameAnalytics::Value(GetGameInfoProxy()->GetRoomHostType())}
	});
#endif

	terminateMPGame(ERR_CODE_CLIENT_LOST_CONN);

	GameNetClientMsgHandler::onClientConnectionLost();
}

void MpGameSurviveNetHandler::onPlayerTeleport(ClientPlayer *player, int targetmap, const WCoord targetpos)
{
	WorldManager *worldMgr = m_root->getWorldMgr();

	player->addRef();
	World *oldworld = player->getWorld();
	if (oldworld == NULL || oldworld->getCurMapID() != targetmap)
	{
		if (oldworld)
		{
			oldworld->getActorMgr()->ToCastMgr()->despawnActor(player);
			oldworld->getActorMgr()->ToCastMgr()->reset();
		}
		if (worldMgr)
		{
			//auto* own_old_world = g_pPlayerCtrl->getWorld();
			bool changeworld = false;
			if (oldworld)
			{
				changeworld = oldworld->getCurMapID() != targetmap;
			}
			World* pworld = nullptr;
			if (changeworld)
			{
				pworld = worldMgr->GoToWorld(oldworld, targetmap, player);
			}
			else
			{
				pworld = worldMgr->getOrCreateWorld(targetmap, player);
			}
			//World *pworld = worldMgr->getOrCreateWorld(targetmap, player);
			if (pworld)
				pworld->getActorMgr()->ToCastMgr()->spawnPlayerAddRef(player);
		}
	}

	PB_ResetPosResponeCH msg;
	msg.set_tick(m_root? m_root->getGameTick(): 0);
	GetGameNetManagerPtr()->sendToHost(PB_SYNC_PLAYER_POS_CH, msg);
	
	player->getLocoMotion()->gotoPosition(targetpos);

	ActorLocoMotion * loc = player->getLocoMotion();
	if (loc)
	{
		PB_PlayerGotoPosCH PlayerGotoPos;
		PlayerGotoPos.set_objid(player->getObjId());
		PB_Vector3* position = PlayerGotoPos.mutable_pos();
		position->set_x(loc->m_Position.x);
		position->set_y(loc->m_Position.y);
		position->set_z(loc->m_Position.z);
		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_GOTOPOS_CH, PlayerGotoPos);
	}
	player->release();
}

void MpGameSurviveNetHandler::terminateMPGame(int errcode)
{
	MpGameSurvive* mp = dynamic_cast<MpGameSurvive*>(m_root);
	if (mp)
	{
		mp->terminateMPGame(errcode);
	}
}

void MpGameSurviveNetHandler::saveCacheChunkData(const ChunkIOCmd &cmd)
{
}

void MpGameSurviveNetHandler::loadCacheChunkData(const ChunkIOCmd &cmd/*const char *chunkCache, int buflen*/)
{
	WorldManager *worldMgr = m_root->getWorldMgr();
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (!worldMgr || !playerCtrl)
	{
		LOG_INFO("WorldMgr == NULL");
		return;
	}
	const char *chunkCache = (const char *)cmd.data;
	int buflen = cmd.datalen;

	ChunkIndex index(cmd.cx, cmd.cz);
	if (chunkCache == NULL)
	{
		m_RequestChunks.push_front(m_CurWaitChunk);
		if (index == m_CurWaitChunk) m_CurWaitChunk = ChunkIndex(MIN_INT, MIN_INT);
		return;
	}
	World *pworld = worldMgr->getWorld(cmd.mapid);
	if (nullptr == pworld)
	{
		return;
	}
	if (index == m_CurWaitChunk) m_CurWaitChunk = ChunkIndex(MIN_INT, MIN_INT);

	Chunk *pchunk = pworld->getChunk(index);
	bool needadd = false;
	if (pchunk == NULL)
	{
		pchunk = ENG_NEW(Chunk)(pworld, index.x, index.z);
		needadd = true;
	}

	if (pchunk)
	{
		if(!needadd) pchunk->onLeaveWorld();

		if (pchunk->loadFromBuffer((const char *)chunkCache, cmd.ext, buflen, pworld, true, 1))
		{
			if (needadd)
			{
				pworld->addChunkFromServer(pchunk, playerCtrl->getChunkViewer());
			}
			else pchunk->onEnterWorld(pworld);
			pworld->populateChunk(pchunk);
		}
		else
		{
			if(needadd)
			{
				OGRE_DELETE(pchunk);
			}
			else pchunk->onEnterWorld(pworld);
		}
	}

	std::vector<PB_PACKDATA_CLIENT*> trunkUpdateMsgVector;
	trunkUpdateMsgVector.swap(mTrunkUpdateMsgVector);
	for (int i = 0; i<(int)trunkUpdateMsgVector.size(); i++)
	{
		handleBlockUpdate2Client(*trunkUpdateMsgVector[i]);
		ENG_DELETE(trunkUpdateMsgVector[i]);
	}
}

void MpGameSurviveNetHandler::handleSyncChunkData2Client(const PB_PACKDATA_CLIENT &pkg)
{
	WorldManager *worldMgr = m_root->getWorldMgr();
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (!worldMgr || !playerCtrl)
	{
		LOG_INFO("WorldMgr playerCtrl == NULL");
		return;
	}

	PB_SyncChunkDataHC syncChunkDataHC;
	bool ret = syncChunkDataHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (!ret)
	{
		LOG_INFO("ChunkData2Client ParseFromArray false !");
	}
	const PB_ChunkSaveDB& chunkData = syncChunkDataHC.chunkdata();

	World *pworld = worldMgr->getWorld(chunkData.mapid());
	if (nullptr == pworld)
	{
		return;
	}

	ChunkIndex index(chunkData.x(), chunkData.z());
	void *chunkCache = NULL;
	if (syncChunkDataHC.initialize() == 3 && syncChunkDataHC.sectionflags() != 0)
	{
		//加载线程加载trunk数据
		if (index == m_CurWaitChunk)
		{	
			ChunkIOCmd cmd;
			memset(&cmd, 0, sizeof(cmd));
			cmd.cmdtype = CIOCMD_LOAD_CACHE_CHUNK;
			cmd.mapid = playerCtrl->getCurMapID();
			cmd.cx = chunkData.x();
			cmd.cz = chunkData.z();
			cmd.ext = chunkData.chunkblob().unziplen();
			strncpy(cmd.path, m_CurWaitChunkMd5.c_str(), 128);
			if(GetWorldManagerPtr() != NULL && GetWorldManagerPtr()->m_ChunkIOMgr != NULL)
			{
				GetWorldManagerPtr()->m_ChunkIOMgr->pushCmd(cmd);
			}
		}
		return;
	}
	if (index == m_CurWaitChunk) m_CurWaitChunk = ChunkIndex(MIN_INT, MIN_INT);

	if (syncChunkDataHC.initialize() > 0 && syncChunkDataHC.sectionflags() == 0)
	{
		pworld->removeChunk(index);

		if (syncChunkDataHC.initialize() == 1)
		{
			auto iter = m_RequestChunks.begin();
			for (; iter != m_RequestChunks.end(); iter++)
			{
				if (*iter == index)
				{
					m_RequestChunks.erase(iter);
					break;
				}
			}
		}
		else if (syncChunkDataHC.initialize() == 2)
		{
			m_RequestChunks.push_back(index);
		}
		return;
	}

	Chunk *pchunk = pworld->getChunk(index);
	bool needadd = false;
	if (syncChunkDataHC.initialize() > 0 && pchunk == NULL)
	{
		pchunk = ENG_NEW(Chunk)(pworld, index.x, index.z);
		needadd = true;
	}

	if (pchunk)
	{
		//if (syncChunkDataHC.initialize() != 3)
		{
			const PB_ChunkBlob& chunkBlob = chunkData.chunkblob();
			if (s_ReceieveFirstChunk)
			{
				char tmpbuf[64];
				sprintf(tmpbuf, "%d", chunkBlob.bloblen() / 1024);
				MINIW::OnStatisticsGameEvent("ReceiveFirstChunk", "KB", tmpbuf);

				GetStatisticsManagerProxy()->joinRoomSucceed(StatisticsActionId::SAID_JoinRoomSucceedEx);
				//MNSandbox::GetGlobalEvent().Emit<>("StatisticsTools_joinRoomSucceed");
				s_ReceieveFirstChunk = false;
			}

			if(!needadd) pchunk->onLeaveWorld(syncChunkDataHC.initialize() == 0);

			if (pchunk->loadFromBuffer(chunkBlob.blobdetail().c_str(), chunkBlob.unziplen(), chunkBlob.bloblen(), pworld, syncChunkDataHC.initialize() != 0, syncChunkDataHC.sectionflags()))
			{
				if (needadd)
				{
					pworld->addChunkFromServer(pchunk, playerCtrl->getChunkViewer());
				}
				else pchunk->onEnterWorld(pworld);
				//else pworld->markBlockForUpdate(pchunk->m_Origin, pchunk->m_Origin+WCoord(CHUNK_BLOCK_X,CHUNK_BLOCK_Y,CHUNK_BLOCK_Z), false);

				pworld->populateChunk(pchunk);

				if (syncChunkDataHC.initialize() == 0)
				{
					pchunk->resetRelightChecks();
				}
			}
			else
			{
				if(needadd)
				{
					OGRE_DELETE(pchunk);
				}
				else pchunk->onEnterWorld(pworld);
			}
			if (playerCtrl)
			{
				char output[16] = {0};
				char hex[33] = {0};
				MINIW::Md5Context context;
				context.begin();
				context.append((const UInt8*)chunkBlob.blobdetail().c_str(), chunkBlob.bloblen());
				context.end((UInt8*)output);
				MINIW::Md5ToHex((char*)hex, (char*)output);
				char path[128];
				sprintf(path, "data/cachetrunk/%d/w%lld_%d_%d_%d_%s", (unsigned int)((playerCtrl->getOWID()>>32) + (playerCtrl->getOWID()&0xffff)*(playerCtrl->getOWID()&0xffff) + (chunkData.x()*chunkData.x()) + chunkData.z())%CIOCMD_SAVE_CACHE_NUM
					,playerCtrl->getOWID(), playerCtrl->getCurMapID() ,chunkData.x(), chunkData.z(), hex);
				ChunkIOCmd cmd;
				memset(&cmd, 0, sizeof(cmd));
				cmd.cmdtype = CIOCMD_SAVE_CACHE_CHUNK;
				cmd.mapid = playerCtrl->getCurMapID();
				cmd.cx = chunkData.x();
				cmd.cz = chunkData.z();
				//cmd.ext = chunkData.chunkblob().unziplen();
				strncpy(cmd.path, path, 128);
				cmd.datalen = chunkBlob.bloblen();
				cmd.data = malloc(cmd.datalen);
				memcpy(cmd.data, (void *)chunkBlob.blobdetail().c_str(), cmd.datalen);
				if(GetWorldManagerPtr() != NULL && GetWorldManagerPtr()->m_ChunkIOMgr != NULL){
					GetWorldManagerPtr()->m_ChunkIOMgr->pushCmd(cmd);
				}
			}
		}
	}
}

EXPORT_SANDBOXENGINE extern WorldContainer *CreateWorldContainerFromChunkContainer(const FBSave::ChunkContainer *pChunkContainer);
void MpGameSurviveNetHandler::handleBlockUpdate2Client(const PB_PACKDATA_CLIENT &pkg)
{
	WorldManager *worldMgr = m_root->getWorldMgr();
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (!worldMgr || !playerCtrl)
	{
		ErrorStringMsg("handleBlockUpdate2Client worldMgr playerCtrl is nil");
		return;
	}

	PB_BlockUpdateHC blockUpdateHC;
	blockUpdateHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	World *pworld = worldMgr->getWorld(blockUpdateHC.mapid());
	if (nullptr != pworld)
	{
		ChunkIndex index(blockUpdateHC.chunkx(), blockUpdateHC.chunkz());
		Chunk *pchunk = pworld->getChunkBySCoord(blockUpdateHC.chunkx(), blockUpdateHC.chunkz());
		if (pchunk == NULL)
		{
			if (index == m_CurWaitChunk && (mTrunkUpdateMsgVector.size() < 6))
			{
				PB_PACKDATA_CLIENT* item = ENG_NEW(PB_PACKDATA_CLIENT)(pkg.MsgCode, pkg.ByteSize);
				if (item != NULL)
				{
					memcpy(item->MsgData, pkg.MsgData, pkg.ByteSize);
					mTrunkUpdateMsgVector.push_back(item);
				}
			}
			return;
		}
		int sx = index.x - 1;
		int sz = index.z - 1;
		int ex = index.x + 1;
		int ez = index.z + 1;

		pworld->cacheChunks(sx, sz, ex, ez);
		std::set<WCoord>oldcontainers; //清掉服务器不存在的container
		// 用来保存改变的block坐标
		std::vector<WCoord> changeBlocksPos; 
		for (int i = 0; i < blockUpdateHC.blocks_size(); i++)
		{
			unsigned int n = blockUpdateHC.blocks(i);
			WCoord pos = WCoord((int)((n >> 12) & 15), (int)(n & 255), (int)((n >> 8) & 15)) + pchunk->m_Origin;
			
			//int blockid = n & ((1 << 16) - 1) & Block::SOC_BLOCKID_MASK;//n & Block::SOC_BLOCKID_MASK;
			//int blockdata = ((n & ((1 << 16) - 1)) >> Block::SOC_BLOCK_DATA) & 15;
			unsigned char blockdataEx = 0;
			if (blockUpdateHC.blocksex_size())
			{
				blockdataEx = blockUpdateHC.blocksex(i);
			}
			Block tmpBlock = Block::changeBlockNewFormat(n >> 16, blockdataEx);
			int blockid = tmpBlock.getResID();
			int blockdata = tmpBlock.getData();

			//客机家园非自建区域灶台相关 code-by: liya
			if (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD)
			{
				int oldid = pworld->getBlockID(pos);
				// 如果是灶台且没有blockid变化，只更新灶台的blockdata
				if (oldid == blockid && (oldid == BLOCK_HEARTH1 || oldid == BLOCK_HEARTH2 || oldid == BLOCK_HEARTH3 || oldid == BLOCK_HEARTH4 || oldid == BLOCK_HEARTH5))
				{
					pworld->setBlockData(pos, blockdata, 2);
				}
				else
				{
					pworld->setBlockAll(pos, blockid, blockdata, 2, true, false, blockdataEx);
				}
			}
			else
			{
				pworld->setBlockAll(pos, blockid, blockdata, 2, true, false, blockdataEx);
			}

			if (pworld->getContainerMgr()->getContainer(pos)) oldcontainers.insert(pos);

			changeBlocksPos.push_back(pos);
		}
		
		pworld->cancelCacheChunks();

		if (changeBlocksPos.size() > 0)
		{
			SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_resetWorkshopConnectCoreBlock",
				SandboxContext(nullptr)
				.SetData_UserObject("changeBlocksPos", changeBlocksPos));
		}

		if (blockUpdateHC.containerbuf().length() > 0)
		{
			const std::string& buffer = blockUpdateHC.containerbuf();
			const FBSave::ChunkContainers *s = flatbuffers::GetRoot<FBSave::ChunkContainers>(buffer.c_str());

			size_t bufflen = buffer.length();
			flatbuffers::Verifier verifier((const uint8_t*)buffer.c_str(), bufflen);
			
			int consize = 0; 
			if (s->Verify(verifier)) // 校验下格式
			{
				consize = (int)s->containers()->size();
			}
			for (int i = 0; i < consize; i++)
			{
				const FBSave::ChunkContainer* src = s->containers()->Get(i);
				WorldContainer* obj = CreateWorldContainerFromChunkContainer(src);
				if (obj)
				{
					if (obj->load(src->container()))
					{
						WorldContainerMgr* containerMgr = dynamic_cast<WorldContainerMgr*>(pworld->getContainerMgr());
						if (containerMgr) {
							auto oldContainer = containerMgr->getContainer(obj->m_BlockPos);
							if (oldContainer && obj->getObjType() == oldContainer->getObjType() && 
								(obj->getObjType() == OBJ_TYPE_EROSION_CONTAINER 
								|| obj->getObjType() == OBJ_TYPE_ARCHITECTURE
								|| obj->getObjType() == OBJ_TYPE_KEYDOOR))
							{
								// auto oldErosionContainer = static_cast<ErosionContainer*>(oldContainer);
								// auto newErosionContainer = static_cast<ErosionContainer*>(obj);
								// if (oldErosionContainer->getHp() != newErosionContainer->getHp())
								// {
								// }
								pworld->addHarmedBlock(obj->m_BlockPos);
							}
							containerMgr->addContainerByServer(obj);
						}
						if (obj->remoteMarkBlockForUpdate())
						{
							pworld->markBlockForUpdate(obj->m_BlockPos, false);
						}
						std::set<WCoord>::iterator iter = oldcontainers.find(obj->m_BlockPos);
						if (iter != oldcontainers.end()) oldcontainers.erase(iter);
					}
					else
					{
						SANDBOX_DELETE(obj);
					}
				}
			}
		}
		std::set<WCoord>::iterator iter = oldcontainers.begin();
		for (; iter != oldcontainers.end(); iter++)
		{
			pworld->getContainerMgr()->destroyContainer(*iter);
		}
		int maxcheck_tick = 4000 + GenRandomInt(2000);
		if (!pworld->GetGodTempleCreateActive() && Rainbow::Timer::getSystemTick() - pchunk->m_CurSaveTick > (unsigned int)maxcheck_tick)
		{
			PB_SyncChunkDataHC syncChunkDataHC;
			syncChunkDataHC.set_initialize(1);
			syncChunkDataHC.set_sectionflags(0xffff);
			PB_ChunkSaveDB* chunkData = syncChunkDataHC.mutable_chunkdata();
			pchunk->savePBBufferUncompress(chunkData, true, 1, 0xffff);
			const PB_ChunkBlob& chunkBlob = chunkData->chunkblob();

			//char output[16] = {0};
			//char hex[33] = {0};
			//MINIW::Md5Context context;
			//context.begin();
			//context.append((const MINIW::UInt8 *)chunkBlob.blobdetail().c_str(), chunkBlob.bloblen());
			//context.end((MINIW::UInt8 *)output);
			//MINIW::Md5ToHex((char*)hex, (char*)output);
			char path[128];
			sprintf(path, "data/cachetrunk/%d/w%lld_%d_%d_%d_", (unsigned int)((playerCtrl->getOWID()>>32) + (playerCtrl->getOWID()&0xffff)*(playerCtrl->getOWID()&0xffff) + (blockUpdateHC.chunkx()*blockUpdateHC.chunkx()) + blockUpdateHC.chunkz())%CIOCMD_SAVE_CACHE_NUM
				,playerCtrl->getOWID(), playerCtrl->getCurMapID() ,blockUpdateHC.chunkx(), blockUpdateHC.chunkz());
			/*
			WriteWholeFile(path, chunkBlob.blobdetail().c_str(), chunkBlob.bloblen()); 
			*/
			ChunkIOCmd cmd;
			memset(&cmd, 0, sizeof(cmd));
			cmd.cmdtype = CIOCMD_SAVE_CACHE_CHUNK;
			cmd.mapid = playerCtrl->getCurMapID();
			cmd.cx = blockUpdateHC.chunkx();
			cmd.cz = blockUpdateHC.chunkz();
			//cmd.ext = chunkBlob.unziplen();
			strncpy(cmd.path, path, 128);
			//cmd.data = (void *)chunkBlob.blobdetail().c_str();
			cmd.datalen = chunkBlob.bloblen();
			cmd.data = malloc(cmd.datalen);
			memcpy(cmd.data, (void *)chunkBlob.blobdetail().c_str(), cmd.datalen);
			cmd.uncompress = true;
			if(GetWorldManagerPtr() != NULL && GetWorldManagerPtr()->m_ChunkIOMgr != NULL){
				GetWorldManagerPtr()->m_ChunkIOMgr->pushCmd(cmd);
			}
			pchunk->m_CurSaveTick = Rainbow::Timer::getSystemTick(); 
		}
	}
}
void MpGameSurviveNetHandler::handleRoleEnterWorld2Client(const PB_PACKDATA_CLIENT &pkg)
{
	//确定g_playercontrol消息过来了
	MNSandbox::MiniReport::StudioReportSandbox(int(ClientLoadingState::CLOAD_WAIT_SANDBOX), 1, false);
	LOG_INFO("MpGameSurviveNetHandler::handleRoleEnterWorld2Client():");
	PB_RoleEnterWorldHC roleEnterWorld;
	roleEnterWorld.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	const PB_WorldDesc &worldDesc = roleEnterWorld.worlddesc();
	const PB_OWGlobal &owGlobal = roleEnterWorld.globalinfo();
	const PB_PlayerInfo &playerInfo = roleEnterWorld.playerinfo();
	const PB_RoleData &roleData = playerInfo.roledata();

	const PB_WorldCreateData &pbCreateData = worldDesc.createdata();

	WorldCreateData createData;
	memset(&createData, 0, sizeof(createData));
	createData.terrtype = pbCreateData.terrtype();
	createData.randseed1 = pbCreateData.randseed1();
	createData.randseed2 = pbCreateData.randseed2();
	createData.rolemodel = pbCreateData.rolemodel();
	createData.xtiles = pbCreateData.tilesx();
	createData.ztiles = pbCreateData.tilesz();
	if(pbCreateData.seedstr().c_str())
	{
		strncpy(createData.seedstr, pbCreateData.seedstr().c_str(), MAX_SEED_STR_LEN);
		createData.seedstr[MAX_SEED_STR_LEN -1] = 0;		
	}
	else strcpy(createData.seedstr, "none");
	//memcpy(createData.seedstr, pbCreateData.seedstr().c_str(), MAX_SEED_STR_LEN);

	WorldManager* worldMgr = ENG_NEW(WorldManager)(worldDesc.worldid(), worldDesc.worldtype(), worldDesc.owneruin(), worldDesc.owneruin(), createData, worldDesc.fromowid(), worldDesc.specialtype(), worldDesc.ctype(), worldDesc.editorsceneswitch());
	SetWorldManagerPtr(worldMgr);
	m_root->setWorldMgr(worldMgr);
	worldMgr->setDeveloperFlag(worldDesc.developerflag());
	worldMgr->setRealOwnerUin(worldDesc.realowneruin());
	worldMgr->setWorldOpenState(worldDesc.worldopen());
	worldMgr->setCurWorldName(worldDesc.worldname().c_str());
	if (worldDesc.has_temptype())
	{
		worldMgr->setTempType(worldDesc.temptype());
	}
	if (worldDesc.has_pwid())
	{
		worldMgr->setWoldPwid(worldDesc.pwid());
	}
	if (worldDesc.has_fissiontype() && worldDesc.has_fissionfrom())
	{
		worldMgr->setFissionInfo(worldDesc.fissiontype(), worldDesc.fissionfrom(), worldDesc.fissionversion());
	}
	if (worldDesc.has_extrainfo())
	{
		worldMgr->setExtraInfo(worldDesc.extrainfo());
	}

	MINIW::ScriptVM::game()->setUserTypePointer("WorldMgr", "WorldManager", worldMgr);
	MINIW::ScriptVM::game()->callFunction("initWorldMgr", "");
	worldMgr->onInit();
	MINIW::ScriptVM::game()->callFunction("ApplySurviveGameConfig", "");
	MINIW::ScriptVM::game()->callFunction("ClientShowWaterMark", "");

	bool newPlayerFlag = !roleEnterWorld.hasrole();

	PlayerControl* playerCtrl = m_root->getPlayerControl();
#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	if (playerInfo.has_nodeid() && !MNSandbox::Config::GetSingleton().IsSandboxMode()) { // studio 地图可以忽略
		SANDBOX_ASSERT(playerInfo.nodeid() != 0);
		if (playerCtrl)
			playerCtrl->SetNodeid(playerInfo.nodeid());

		LOG_INFO("[player_nodeid] recv player nodeid: %d , objid:%d", playerInfo.nodeid(), playerInfo.objid());
	}
#endif//SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	clientLogin(playerInfo.objid(), owGlobal, roleData);
	
	if (playerCtrl)
	{
		if (playerCtrl->getBody())
		{
			playerCtrl->getBody()->setActSeqID(playerInfo.actseqid());
			playerCtrl->getBody()->setCurAnim(playerInfo.anim(), 0);
			playerCtrl->getBody()->setCurAnim(playerInfo.anim1(), 1);
			if (playerInfo.bodycolor() > 0)
				playerCtrl->getBody()->setBodyColor(playerInfo.bodycolor(), false);
			if (playerInfo.customscale() > 0.0001f)
				playerCtrl->setCustomScale(playerInfo.customscale());
			
			if (playerInfo.sawtooth_size() > 0)
			{
				auto thornBall = playerCtrl->sureThornBallComponent();
				if (thornBall != nullptr)
				{
					auto sawtooh = playerInfo.sawtooth();
					for (int i = 0; i < sawtooh.size(); i++)
					{
						const PB_Vector3& pos = sawtooh.Get(i).pos();
						thornBall->setThornAnchorId(sawtooh.Get(i).sawtoothid(), Rainbow::Vector3f(pos.x(), pos.y(), pos.z()));
					}
					thornBall->createThornBall();
				}
			}
		}
		playerCtrl->setNewPlayer(newPlayerFlag);
		//客机家园出生点相关设定
		SandboxResult homeresult = SandboxEventDispatcherManager::GetGlobalInstance().
			Emit("Homeland_GetSpawnRotateRaw", SandboxContext(nullptr).SetData_Number("SpawnPosType",0));
		if (homeresult.IsSuccessed() && playerCtrl->getLocoMotion())
		{
			float rotateRaw = float(homeresult.GetData_Number("rotateRaw"));
			playerCtrl->getLocoMotion()->setRotateRaw(rotateRaw);
		}
	}

	const PB_SkillCDData& skillCDData = roleEnterWorld.skillcddata();
	for (int i = 0; i < skillCDData.itemid_size(); i++)
	{
		if (playerCtrl) 
		{
			playerCtrl->setSkillCD(skillCDData.itemid(i), skillCDData.cd(i));
		}
	}
	auto skillExtendCDData = roleEnterWorld.skillexpandcddatagather();
	playerCtrl->loadSkillExtendCDCompFromPB(&skillExtendCDData);

	for (int i = 0; i < roleEnterWorld.unlockitems_size(); i++)
	{
		if (playerCtrl) playerCtrl->addUnlockItem(roleEnterWorld.unlockitems(i));
	}

	std::string url = roleEnterWorld.url();
	if (newPlayerFlag && !url.empty())
	{
		MpGameSurvive* mp = dynamic_cast<MpGameSurvive*>(m_root);
		if (mp)
		{
			mp->downloadIntroInfo(url);
		}
		m_root->setCurLadingState(CLOAD_WAIT_INTRO);
	}
	else
	{
		MNSandbox::MiniReport::StudioReportSandbox(int(ClientLoadingState::CLOAD_WAIT_SANDBOX), 2, false);
		m_root->setCurLadingState(CLOAD_WAIT_SANDBOX); //m_root->setCurLadingState(CLOAD_WAIT_EDITOR);
	}

	GameNetManager *gnm = GetGameNetManagerPtr();
	if (gnm)
	{
		MNSandbox::GetGlobalEvent().Emit<int>("RoomManager_setForeRoomUin", gnm->getHostUin());
	}
	MINIW::OnStatisticsGameEvent("RoleEnterWorld");
	s_ReceieveFirstChunk = true;

	if (playerCtrl)
	{
		playerCtrl->updateTask(PAY_ITEM, 0, 0);

		//基础模型、属性
		int teamId = playerCtrl->getTeam();
		playerCtrl->setBaseModel(teamId);
		PlayerAttrib* playerAttr = dynamic_cast<PlayerAttrib*>(playerCtrl->getPlayerAttrib());
		if (playerAttr)
			playerAttr->initPlayerBaseAttr(teamId);
	}

	//获取玩家存档数据
	if (worldMgr->m_RuleMgr)
	{
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_UgcModeLoadCloudVar",
			MNSandbox::SandboxContext(nullptr)
			.SetData_Number("uin", playerCtrl->getUin()));
	}

	MINIW::ScriptVM::game()->callFunction("OnMpClientEnterGame", "");

#ifndef IWORLD_SERVER_BUILD
	// 玩家真正进入游戏世界成功埋点
	std::string game_session_id = GetIWorldConfigProxy()->getGameSessionId();
	if (game_session_id.empty()) {
		game_session_id = GetIWorldConfigProxy()->generateGameSessionId();
	}
	GameAnalytics::SetGameSessionInfo(game_session_id);
	
	long long mapId = GetWorldManagerPtr() ? GetWorldManagerPtr()->getWorldId() : 0;
	std::string roomid = GetGameInfoProxy()->GetGameVar("serverid");

	GameAnalytics::CommonProperties properties;
	properties.roomid = roomid;
	properties.mapid = std::to_string(mapId);
	GameAnalytics::SetGameServerInfo(properties);

	
	GameAnalytics::TrackEvent("player_enter_room_success", {
		{"uin", GameAnalytics::Value(roleData.uin())},
		{"world_id", GameAnalytics::Value(int(worldDesc.worldid()))},
		{"host_uin", GameAnalytics::Value(GameNetManager::getInstance()->getHostUin())},
		{"room_host_type", GameAnalytics::Value(GetGameInfoProxy()->GetRoomHostType())},
		{"has_role", GameAnalytics::Value(roleEnterWorld.has_hasrole() ? roleEnterWorld.hasrole() : false)}
	});

	
#endif

	if(roleEnterWorld.has_teleportmsg())
	{
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("SandboxCloudSetvice_OnPlayerTeleport",
			MNSandbox::SandboxContext(nullptr)
			.SetData_Number("uin", roleEnterWorld.uin())
			.SetData_String("msg", roleEnterWorld.teleportmsg()));
	}

	if (playerCtrl)
	{
		PB_AllSingleBuildDataCH ch;
		ch.set_uin(playerCtrl->getUin());
		GetGameNetManagerPtr()->sendToHost(PB_AllSingleBuildData_CH, ch);

		PB_AllSingleBuildDataCH Workbenchch;
		Workbenchch.set_uin(playerCtrl->getUin());
		GetGameNetManagerPtr()->sendToHost(PB_SocWorkbench_CH, Workbenchch);

		//SocRevivePointComponent* SocRevivePoint = playerCtrl->GetComponent<SocRevivePointComponent>();
		//if (SocRevivePoint)
		//{
		//	SocRevivePoint->ReqAllPosints();
		//}
	}
	//add client replicator
	//auto replicatorRoot = MNSandbox::GetCurrentReplicatorRoot();
	//if (replicatorRoot) {
	//	replicatorRoot->RegisterClientReplicator(roleData.uin());
	//}
}
void MpGameSurviveNetHandler::handleGeneralEnterAoi2Client(const PB_PACKDATA_CLIENT& pkg)
{
	OPTICK_EVENT();
	PB_GeneralEnterAOIHC actorEnterAOIHC;
	actorEnterAOIHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	// int sizelen = pkg.ByteSize;
	// sizelen++;
	WorldManager* worldMgr = m_root->getWorldMgr();
	long long objId = actorEnterAOIHC.objid();
	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL)
	{
		return;
	}
	if (objId == playerCtrl->getObjId())
	{
		return;
	}

	ClientActor* actor = objId2ActorOnClient(objId);

	if (actor != nullptr) //already in list? sync prolbem.
	{
		LOG_SEVERE("[%lld] re-- entered!", objId);
		
		//下面despawnActor可能会崩溃
		if (actor->IsObject())	return;

		//actor->setNeedClear(2);
		//actorLeaveGame(actor);
		if (actor->getWorld() != NULL && actor->getWorld()->getActorMgr() != NULL) {
			actor->getWorld()->getActorMgr()->ToCastMgr()->despawnActor(actor);
		}
	}
	// const PB_ActorMob &mob = actorEnterAOIHC.actormob();

	actor = CreateActorFromGeneralEnterAOIHC(actorEnterAOIHC);
	assert(actor);
	if (actor == NULL)
	{
		LOG_SEVERE("actor IS NULL !");
		return;
	}
	{
		OPTICK_EVENT("LoadFromPB");
		int ret = actor->LoadFromPB(actorEnterAOIHC);
		if (ret != 0)
		{
			actor->release();
			LOG_SEVERE("actor LOAD FROM PB failed !");
			return;
		}
	}

	actor->SetObjId(objId);

	int mapId = actorEnterAOIHC.mapid();
	if (worldMgr == nullptr)
		return;
	World* pworld = worldMgr->getWorld(mapId);
	if (nullptr == pworld)
	{
		LOG_SEVERE("actor enter view:  cannot find world %d", mapId);
		return;
	}

	pworld->getActorMgr()->ToCastMgr()->spawnActor(actor);

	if (actor->getBody())
		actor->getBody()->setNeedUpdateAnim(false);

	//actor:播放特效************************************************
	if (actor->getBody() || OBJ_TYPE_THROWABLE == actor->getObjType() || actor->getObjType() == OBJ_TYPE_DROPITEM)
	{
		if (actorEnterAOIHC.effectlist_size() > 0) {
			auto effectComponent = actor->getEffectComponent();
			if (effectComponent) {
				OPTICK_EVENT("playEffects");
				OPTICK_TAG("effects", actorEnterAOIHC.effectlist_size());
				for (int i = 0; i < actorEnterAOIHC.effectlist_size(); i++)
				{
					const PB_AOIBodyEffectBrief& effectBrief = actorEnterAOIHC.effectlist(i);
					int                       effectID = effectBrief.effectid();
					float                     effectScale = effectBrief.effectscale() / 1000.0;
					float effectlooptime = effectBrief.has_effecttime() ? effectBrief.effecttime() : -1.0f;
					if (effectID > 0)
					{
						ParticleDef* def = GetDefManagerProxy()->getParticleDef(effectID);
						if (def)
						{
							//用sprintf转一次string是什么意思？
							//char szName[128];
							//snprintf(szName, 128, "%s", def->EffectName.c_str());
							effectComponent->playBodyEffectForTrigger(def->EffectName.c_str(), effectScale,false, effectlooptime);
						}
					}
				}
			}

		}
	//***************************************************************

		{
			if (actorEnterAOIHC.soundlist_size() > 0) {
				OPTICK_EVENT("playSounds");
				OPTICK_TAG("sounds", actorEnterAOIHC.soundlist_size());
				//Player:播放音效
				auto soundcomp = actor->getSoundComponent();
				if (soundcomp)
				{
					for (int i = 0; i < actorEnterAOIHC.soundlist_size(); i++)
					{
						const PB_AOIEffectTriggerSound& triggerSound = actorEnterAOIHC.soundlist(i);
						const char* path = triggerSound.name().c_str();
						float fVolume = triggerSound.volume() / 1000.0;
						float fPitch = triggerSound.pitch() / 1000.0;
						float bIsLoop = triggerSound.has_isloop();
						int position = triggerSound.position();
						soundcomp->playSoundByTrigger(path, fVolume, fPitch, bIsLoop, false, position);
					}
				}
			}
		}

		if (actorEnterAOIHC.has_actoritem())
		{
			const PB_ActorItem& actorItem = actorEnterAOIHC.actoritem();
			//相片掉落物要先去下载
			if (actorItem.has_itemid() && actorItem.itemid() == ITEM_POLAROID_PHOTO && actorItem.has_userdatastr())
			{
				const std::string& userdataStr = actorItem.userdatastr();
				jsonxx::Object infoObj;
				infoObj << "objid" << objId;
				infoObj << "userdata" << userdataStr;
				playerCtrl->downloadPolaroidPhoto(infoObj.json());
			}
		}

		actor->update(0);
	}
}
void MpGameSurviveNetHandler::handleActorMoveV22Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_ActorMoveV2HC actorMoveHC;
	actorMoveHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	long long ObjId = actorMoveHC.objid();
	ObjId = ClientActor::UnpackObjId(ObjId);
	ClientActor* actor = objId2ActorOnClient(ObjId);

	if (actor)
	{
		int flag = 0;  // 标记本次同步是否包含位置/头角度信息
		WCoord cur_pos = actor->getPosition();
		if (actorMoveHC.position_size() == 3)
		{
			// cur_pos = MPVEC2WCoord(actorMoveHC.position());
			cur_pos.x = actorMoveHC.position(0);
			cur_pos.y = actorMoveHC.position(1);
			cur_pos.z = actorMoveHC.position(2);

			flag |= MF_POSITION;
		}
		// 		if (actor->isRiding() || actor->getBindingActor())
		// 		{
		// 			WCoord pos = actor->getPosition();
		// 		}

		float yaw = actor->getFaceYaw();
		float pitch = actor->getFacePitch();
		if (actorMoveHC.has_yaw_pitch())
		{
			flag |= MF_YAW_PITCH;
			uint32_t yaw_pitch = actorMoveHC.yaw_pitch();
			yaw = AngleChar2Float((yaw_pitch & 0xff00) >> 8);
			pitch = AngleChar2Float(yaw_pitch & 0xff);
		}
		actor->setNetMoveFlag(flag);
		actor->moveToPosition(cur_pos, yaw, pitch, 3);
		actor->clearNetMoveFlag();

		ActorLocoMotion* locmove = actor->getLocoMotion();
		if (locmove)
		{
			locmove->setOnGround(!actorMoveHC.has_changeflags());
		}
	}
}

void MpGameSurviveNetHandler::handleActorModelChgClient(const PB_PACKDATA_CLIENT& pkg)
{
	PB_ActorModelChange modelChg;
	modelChg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	long long ObjId = modelChg.objid();
	ObjId = ClientActor::UnpackObjId(ObjId);
	ClientActor* actor = objId2ActorOnClient(ObjId);
	if (!actor) return;
	auto modelcom = actor->SureModelComponent();
	modelcom->UnSerializeAndLoadModelKV((char*)modelChg.modelcomponent().data(), modelChg.modelcomponent().size());
}

void MpGameSurviveNetHandler::handleActorMoveV32Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_ActorMoveV3HC_Batch batch;
	batch.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	for (size_t i = 0; i < batch.movebatch_size(); i++)
	{
		const PB_ActorMoveV2HC& actorMoveHC = batch.movebatch(i);

		long long ObjId = actorMoveHC.objid();
		ObjId = ClientActor::UnpackObjId(ObjId);
		ClientActor* actor = objId2ActorOnClient(ObjId);

		if (actor)
		{
			int flag = 0;  // 标记本次同步是否包含位置/头角度信息
			WCoord cur_pos = actor->getPosition();
			if (actorMoveHC.position_size() == 3)
			{
				// cur_pos = MPVEC2WCoord(actorMoveHC.position());
				cur_pos.x = actorMoveHC.position(0);
				cur_pos.y = actorMoveHC.position(1);
				cur_pos.z = actorMoveHC.position(2);

				flag |= MF_POSITION;
			}
			// 		if (actor->isRiding() || actor->getBindingActor())
			// 		{
			// 			WCoord pos = actor->getPosition();
			// 		}

			float yaw = actor->getFaceYaw();
			float pitch = actor->getFacePitch();
			if (actorMoveHC.has_yaw_pitch())
			{
				flag |= MF_YAW_PITCH;
				uint32_t yaw_pitch = actorMoveHC.yaw_pitch();
				yaw = AngleChar2Float((yaw_pitch & 0xff00) >> 8);
				pitch = AngleChar2Float(yaw_pitch & 0xff);
			}
			actor->setNetMoveFlag(flag);
			actor->moveToPosition(cur_pos, yaw, pitch, 3);
			actor->clearNetMoveFlag();

			ActorLocoMotion* locmove = actor->getLocoMotion();
			if (locmove)
			{
				locmove->setOnGround(!actorMoveHC.has_changeflags());
			}
		}
	}
}


void MpGameSurviveNetHandler::handleFullrotActorMove2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_FullrotActorMoveHC actorMoveHC;
	actorMoveHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	long long ObjId = actorMoveHC.objid();
	ClientActor* actor = objId2ActorOnClient(ObjId);

	if (actor)
	{
		WCoord cur_pos = actor->getPosition();

		if (actorMoveHC.has_position())
		{
			cur_pos = MPVEC2WCoord(actorMoveHC.position());
		}
		// 		if (actor->isRiding() || actor->getBindingActor())
		// 		{
		// 			WCoord pos = actor->getPosition();
		// 		}

		if (actorMoveHC.has_yaw())
		{
			Rainbow::Quaternionf rot;
			rot.FromUInt32(actorMoveHC.yaw());
			actor->moveToPosition(cur_pos, rot, 3);
		}
		else
		{
			float cur_yaw = actor->getFaceYaw();
			float cur_pitch = actor->getFacePitch();
			actor->moveToPosition(cur_pos, cur_yaw, cur_pitch, 3);
		}

		ActorLocoMotion* locmove = actor->getLocoMotion();
		locmove->setOnGround(!actorMoveHC.has_changeflags());
	}
}
void MpGameSurviveNetHandler::handleActorMotionV22Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_ActorMotionV2HC actorMotionHC;
	actorMotionHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	long long ObjId = actorMotionHC.objid();
	ObjId = ClientActor::UnpackObjId(ObjId);
	ClientActor* actor = objId2ActorOnClient(ObjId);
	if (actor != nullptr)
	{
		Rainbow::Vector3f& motion = actor->getLocoMotion()->m_Motion;
		/*
		motion.x += pkg.Body.ActorMotionHC.x;
		motion.y += pkg.Body.ActorMotionHC.y;
		motion.z += pkg.Body.ActorMotionHC.z;
		*/

		motion.x = actorMotionHC.x() / 1000.0;
		motion.y = actorMotionHC.y() / 1000.0;
		motion.z = actorMotionHC.z() / 1000.0;
		if (actorMotionHC.has_ischangepos())
		{
			actor->getLocoMotion()->doMoveStep(motion);
		}
		actor->MotionHasChange();
	}
}
void MpGameSurviveNetHandler::handleActorEnterAoi2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActorEnterAOIHC actorEnterAOIHC;
	actorEnterAOIHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	MpGameSurvive* mp = dynamic_cast<MpGameSurvive*>(m_root);
	int sizelen = pkg.ByteSize;
	sizelen++;
	WorldManager *worldMgr = m_root->getWorldMgr();
	long long objId = actorEnterAOIHC.objid();
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL)
	{
		return; 
	}
	if (objId == playerCtrl->getObjId())
	{
		return;
	}

	ClientActor *actor = objId2ActorOnClient(objId);
	
	if (actor != nullptr) //already in list? sync prolbem.
	{
		LOG_SEVERE("[%lld] re-- entered!", objId);
		//actor->setNeedClear(2);
		//actorLeaveGame(actor);
		if(actor->getWorld() != NULL && actor->getWorld()->getActorMgr() != NULL){
				actor->getWorld()->getActorMgr()->ToCastMgr()->despawnActor(actor);
		}
	}

	if (PB_ACTORTYPEROLE == actorEnterAOIHC.actortype())
	{
		const PB_ActorRoleInfo &actorRole = actorEnterAOIHC.actorinfo().roleinfo();
		if (worldMgr == nullptr) 
		{ 
			return; 
		}
		World *pworld = worldMgr->getWorld(actorRole.player().roledata().pos().map());
		if (nullptr == pworld)
		{
			LOG_SEVERE("player enter view,  cannot find world: %d", actorRole.player().roledata().pos().map());
			return;
		}

		int playerindex = ComposePlayerIndex(actorRole.info().model(), 0, actorRole.info().skinid());
		const char *customjson = NULL;
		if(actorRole.info().has_customjson()) customjson = actorRole.info().customjson().c_str();

		ClientPlayer *player = SANDBOX_NEW(ClientPlayer);
		player->SetObjId(objId);
#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
		{
			const auto& playerinfo = actorRole.player();
			if (actorRole.player().has_nodeid()) {
				SANDBOX_ASSERT(actorRole.player().nodeid() != 0);
				player->SetNodeid(actorRole.player().nodeid());
				LOG_INFO("[player_nodeid] recv player nodeid: %d , objid:%d", actorRole.player().nodeid(), objId);
			}
		}
#endif//SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
		MNSandbox::GlobalNotify::GetInstance().NotifyClientAoiEnterRoom(objId);
		player->init(actorRole.player().roledata().uin(), actorRole.info().nickname().c_str(), playerindex, customjson);
		player->m_BPTitle = actorRole.info().bptitle();	// 悦享赛事称号 20230616 by wuyuwang
		if (actorRole.player().sawtooth_size() > 0)
		{
			auto thornBall = player->sureThornBallComponent();
			if (thornBall != nullptr)
			{
				auto sawtooh = actorRole.player().sawtooth();
				for (int i = 0; i < sawtooh.size(); i++)
				{
					const PB_Vector3& pos = sawtooh.Get(i).pos();
					thornBall->setThornAnchorId(sawtooh.Get(i).sawtoothid(), Rainbow::Vector3f(pos.x(), pos.y(), pos.z()));
				}
				thornBall->createThornBall();
			}
		}
		if (long long objId = actorRole.player().curdisplayhorseobjid())
		{
			player->setCurAccountHorse(objId);
		}

		PlayerAttrib* playerAttr = dynamic_cast<PlayerAttrib*>(player->getPlayerAttrib());
		if (playerAttr)
		{
			if (actorEnterAOIHC.has_offline())
			{
				playerAttr->setOffLine(actorEnterAOIHC.offline());
			}
		}
		
		pworld->getActorMgr()->ToCastMgr()->spawnPlayerAddRef(player);
		auto info = mp->findPlayerInfoByUin(objId);
		//团队人员要设置名字要让自己的端穿墙
		PlayerTeamComponent *selfteamcom = g_pPlayerCtrl->GetComponent<PlayerTeamComponent>();
		if (selfteamcom)
		{
			player->changeSocTeamConfig(selfteamcom->checkTeam(player->getUin()));
		}

		//if (info != NULL) {
		//	player->setTeam(info->teamid);
		//}
		//if (actorRole.player().has_teamid() && actorRole.player().teamid() != player->getTeam())
		//{
		//	player->setTeam(actorRole.player().teamid());
		//}
		player->setBaseModel(player->getTeam());
		player->reStoreRoleData(actorRole.player().roledata());

		player->getBody()->setActSeqID(actorRole.player().actseqid());
		player->getBody()->setCurAnim(actorRole.player().anim(), 0);
		player->getBody()->setCurAnim(actorRole.player().anim1(), 1);
		player->getBody()->setCurAnimWeapon(actorRole.player().animweapon(), 0);
		unsigned int color = actorRole.player().bodycolor();
		if(color > 0)
			player->getBody()->setBodyColor(color, false);
		float customscale = actorRole.player().customscale();
		if (customscale > 0.0001f)
		{
			player->setCustomScale(customscale);
		}
		player->setSpectatorMode((PLAYER_SPECTATOR_MODE)actorEnterAOIHC.spectator_mode());
		player->setSpectatorType((PLAYER_SPECTATOR_TYPE)actorEnterAOIHC.spectator_type());
		player->setHookObj(actorEnterAOIHC.hookid());
		if (actorEnterAOIHC.has_playmode())
		{
			player->setOPWay(actorEnterAOIHC.playmode());
		}
		if (actorEnterAOIHC.has_playoperate())
		{
			player->setOperate(actorEnterAOIHC.playoperate());
		}
		if (actorEnterAOIHC.has_childuuid())
		{
			ActorBasketBall *bskBall = dynamic_cast<ActorBasketBall*>(pworld->getActorMgr()->ToCastMgr()->findActorByWID(actorEnterAOIHC.childuuid()));
			if (bskBall)
			{
				auto bindAComponent = bskBall->getBindActorCom();
				if (bindAComponent)
				{
					bindAComponent->setBindInfo(player->getObjId(), WCoord(0, 0, 0), true);
				}
			}
		}

		//player:播放特效
		for (int i = 0; i < actorRole.player().effectlist_size(); i++)
		{
			const PB_BodyEffectBrief& effectBrief = actorRole.player().effectlist(i);
			int effectID = effectBrief.effectid();
			float effectScale = effectBrief.effectscale();
			float effectTime = effectBrief.has_effecttime() ? effectBrief.effecttime():-1.0f;

			if (effectID > 0)
			{
				ParticleDef* def = GetDefManagerProxy()->getParticleDef(effectID);
				if (def)
				{
					char szName[128] = "";
					sprintf(szName, "%s", def->EffectName.c_str());

					if (player)
					{
						auto effectComponent = player->getEffectComponent();
						if (effectComponent)
						{
							effectComponent->playBodyEffectForTrigger(szName, effectScale,false, effectTime);
						}
					}
				}
			}
		}

		//Player:播放音效
		for (int i = 0; i < actorRole.player().soundlist_size(); i++)
		{
			const PB_EffectTriggerSound& triggerSound = actorRole.player().soundlist(i);
			const char* path = triggerSound.name().c_str();
			float fVolume = triggerSound.volume();
			float fPitch = triggerSound.pitch();
			float bIsLoop = triggerSound.isloop();
			int position = triggerSound.position();
			if (player)
			{
				auto sound = player->getSoundComponent();
				if (sound)
				{
					sound->playSoundByTrigger(path, fVolume, fPitch, bIsLoop, false, position);
				}
			}
		}

		player->update(0);
		player->release();

		if(g_pPlayerCtrl)
		{
			g_pPlayerCtrl->CheckSpectatorPlayerShow();
			g_pPlayerCtrl->setMainPlayerAttrib();
		}



		if (actorRole.player().has_fishing())
		{
			auto fish = actorRole.player().fishing();
			auto fishingCmp = player->getFishingComponent();
			if (fishingCmp)
			{
				if (fish.has_fishingitemid())
					fishingCmp->setResultFromHost(fish.fishingitemid());
				if (fish.has_hookid())
					fishingCmp->setHookIdFromHost(fish.hookid());
				if (fish.has_fishingstate())
					fishingCmp->forceChanegStateFromHost(fish.fishingstate());
			}
		}

		MINIW::ScriptVM::game()->callFunction("ReqCurUseAchieveByUin", "i", objId);
		//2021-12-20 codeby: wangyang 会员图标
		char buffer[64];
		sprintf(buffer, "%lld", objId);
		MINIW::ScriptVM::game()->callFunction("OnPlayerEnter", "s", buffer);

	}
	else if (PB_ACTORTYPEGENERAL == actorEnterAOIHC.actortype())
	{
		const PB_ActorInfo& actorInfo = actorEnterAOIHC.actorinfo();
		const PB_ActorGeneral& actorGeneral = actorInfo.actorgeneral();
		if ((!actorGeneral.has_actordetail()) || actorGeneral.actordetail().size() != actorGeneral.infolen())
		{
			return;	
		}
		ClientActor *actor = NULL;
		if (actorGeneral.has_actortype())
		{
			jsonxx::Object obj;
			obj.parseBinary((unsigned char*)actorGeneral.actordetail().c_str(), actorGeneral.infolen());
			IClientActor* iactor = GetWorldManagerPtr()->cloneActor(actorGeneral.actortype());
			actor = iactor ? iactor->GetActor() : nullptr;
			if (actor)
			{
				actor->load(obj, CommonUtil::GetInstance().GetGameVersionInt());
			}
		}
		else
		{
			actor = ClientActor::createFromBuffer((void*)actorGeneral.actordetail().c_str(), CommonUtil::GetInstance().GetGameVersionInt());
		}
		if (actor == NULL)
		{
			LOG_SEVERE("actor IS NULL !");
			return;
		}
		actor->SetObjId(objId);

		int mapId = actorGeneral.mapid();
		if(worldMgr == nullptr) return;
		World *pworld = worldMgr->getWorld(mapId);
		if (nullptr == pworld)
		{
			LOG_SEVERE("actor enter view:  cannot find world %d", mapId);
			return;
		}

		pworld->getActorMgr()->ToCastMgr()->spawnActor(actor);

		if (actor->getBody())
			actor->getBody()->setNeedUpdateAnim(false);

		//actor:播放特效************************************************
		if (actor->getBody() || OBJ_TYPE_THROWABLE == actor->getObjType())
		{
			for (int i = 0; i < actorInfo.roleinfo().player().effectlist_size(); i++)
			{
				const PB_BodyEffectBrief& effectBrief = actorInfo.roleinfo().player().effectlist(i);
				int effectID = effectBrief.effectid();
				float effectScale = effectBrief.effectscale();
				float effectlooptime = effectBrief.has_effecttime() ? effectBrief.effecttime() : -1.0f;

				if (effectID > 0)
				{
					ParticleDef* def = GetDefManagerProxy()->getParticleDef(effectID);
					if (def)
					{
						char szName[128] = "";
						sprintf(szName, "%s", def->EffectName.c_str());

						if (actor)
						{
							auto effectComponent = actor->getEffectComponent();
							if (effectComponent)
							{
								effectComponent->playBodyEffectForTrigger(szName, effectScale,false, effectlooptime);
							}
						}
							
					}
				}
			}
		}
		//***************************************************************

		//Player:播放音效
		for (int i = 0; i < actorInfo.roleinfo().player().soundlist_size(); i++)
		{
			const PB_EffectTriggerSound& triggerSound = actorInfo.roleinfo().player().soundlist(i);
			const char* path = triggerSound.name().c_str();
			float fVolume = triggerSound.volume();
			float fPitch = triggerSound.pitch();
			float bIsLoop = triggerSound.isloop();
			int position = triggerSound.position();
			if (actor)
			{
				auto sound = actor->getSoundComponent();
				if (sound)
				{
					sound->playSoundByTrigger(path, fVolume, fPitch, bIsLoop, false, position);
				}
			}
		}

		actor->update(0);
	}
}

void MpGameSurviveNetHandler::handleActorLeaveAoi2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActorLeaveAOIHC actorLeaveAOIHC;
	actorLeaveAOIHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	long long objId = actorLeaveAOIHC.objid();
	if(playerCtrl == NULL) return;
	if (objId == playerCtrl->getObjId())
	{
		return;
	}

	ClientActor *actor = objId2ActorOnClient(objId);
	if (actor != nullptr)
	{ 
		if (actor->IsKindOf<ClientPlayer>())
		{
			MNSandbox::GlobalNotify::GetInstance().NotifyClientAoiLeaveRoom(objId);
		}
		//MNSandbox::GlobalNotify::GetInstance().NotifyClientAoiLeaveRoom(objId);

		if(actor->getWorld() != NULL && actor->getWorld()->getActorMgr() != NULL){
			actor->getWorld()->getActorMgr()->ToCastMgr()->despawnActor(actor);
		}
	}
	else
	{
		//LOG_SEVERE("leave view: cannot find actor");
	}
}

void MpGameSurviveNetHandler::handleActorMove2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActorMoveHC actorMoveHC;
	actorMoveHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	long long ObjId = actorMoveHC.objid();
	ClientActor *actor = objId2ActorOnClient(ObjId);

	if (actor)
	{
		const PB_MoveMotion &moveMotion = actorMoveHC.movemotion();
		WCoord pos = MPVEC2WCoord(moveMotion.position());
// 		if (actor->isRiding() || actor->getBindingActor())
// 		{
// 			WCoord pos = actor->getPosition();
// 		}

		if(actor->getLocoMotion()->needFullRotation() && moveMotion.pitch() == 10000)
		{
			Rainbow::Quaternionf rot;
			rot.FromUInt32(moveMotion.yaw());
			//rot.decompressFromInt(moveMotion.yaw());
			actor->moveToPosition(pos, rot, 3);
		}
		else
		{
			actor->moveToPosition(pos, AngleChar2Float(moveMotion.yaw()), AngleChar2Float(moveMotion.pitch()), 3);
		}

		bool onground = (moveMotion.changeflags() & 8) != 0;
		ActorLocoMotion *locmove = actor->getLocoMotion();		
		if (locmove)
		{
			locmove->setOnGround(onground);
		}
// 		locmove->m_OnGround = moveMotion.has_changeflags();
	}
}

void MpGameSurviveNetHandler::handleVehicleMove2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_VehicleMoveHC vehicleMoveHC;
	vehicleMoveHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	long long ObjId = vehicleMoveHC.objid();
	ClientActor *actor = objId2ActorOnClient(ObjId);
	if (actor)
	{
		VehicleSyncPosDesc desc;
		PB_VehiclePosDesc descPB;
		std::vector<VehicleSyncPosDesc> chassisServerPos;
		std::vector<VehicleSyncPosDesc> wheelServerPos;

		for (int i=0; i<vehicleMoveHC.chassispos_size(); i++)
		{
			descPB = vehicleMoveHC.chassispos(i);
			desc.m_PPosition = MPVEC2WCoord(descPB.position());
			desc.m_RotateQuat = MPVEC2Quaternion(descPB.rotatequat());
			chassisServerPos.push_back(desc);
		}
		
		for (int i=0; i<vehicleMoveHC.wheelpos_size(); i++)
		{
			descPB = vehicleMoveHC.wheelpos(i);
			desc.m_PPosition = MPVEC2WCoord(descPB.position());
			desc.m_RotateQuat = MPVEC2Quaternion(descPB.rotatequat());
			wheelServerPos.push_back(desc);
		}
		ActorVehicleAssemble *vehicle = static_cast<ActorVehicleAssemble *>(actor);
		int isDouble = g_pPlayerCtrl == vehicle->getRiddenByActor() ? 2 : 1;
		int ticks = 3;
		//偏离中心放置载具时，云服驾驶员原地自转会使轮子出现位置偏差
		//MpGameSurvive* mp = dynamic_cast<MpGameSurvive *>(ClientGameManager::getInstance()->getCurGame());
		//if (mp)
		//{
		//	ticks += mp->getNetDelayTick();
		//	ticks = ticks >= 6 ? ticks * 5 * isDouble : ticks * isDouble;
		//}
		vehicle->syncServerPos(chassisServerPos, wheelServerPos, ticks, 3);

		if (chassisServerPos.size())
		{
			vehicle->moveToPosition(WCoord(chassisServerPos[0].m_PPosition.x, chassisServerPos[0].m_PPosition.y, chassisServerPos[0].m_PPosition.z), chassisServerPos[0].m_RotateQuat, ticks);
		}

		for (int i = 0; i < vehicleMoveHC.strusterspower_size(); i++)
		{
			PB_VehicleSTrustersPowerLevel STrusterData = vehicleMoveHC.strusterspower(i);
			WCoord pos = MPVEC2WCoord(STrusterData.position());
			MINIW::SThrusterParam * pSTruster = vehicle->getSThrusterParamWithPos(pos);
			if (pSTruster)
			{
				pSTruster->m_nChangeState = STrusterData.powerlevel();
				pSTruster->m_fCurPower = (float)STrusterData.curpower();
			}
		}
	}
}

void MpGameSurviveNetHandler::handleTrainMove2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_TrainMoveHC trainMoveHC;
	trainMoveHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	long long ObjId = trainMoveHC.objid();
	ActorTrainCar *car = dynamic_cast<ActorTrainCar *>(objId2ActorOnClient(ObjId));
	if (car)
	{
		WCoord railknot = MPVEC2WCoord(trainMoveHC.railknot());
		car->onSetRailFromServer(railknot, trainMoveHC.outindex(), trainMoveHC.curvet(), trainMoveHC.headcar(), trainMoveHC.tailcar(), trainMoveHC.carreverse());
	}
}

void MpGameSurviveNetHandler::handleSyncSaveWorld(const PB_PACKDATA_CLIENT &pkg)
{
}

void MpGameSurviveNetHandler::handleActorRevive2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActorReviveHC actorReviveHC;
	actorReviveHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if(playerCtrl == NULL) return;
	if (actorReviveHC.objid() == playerCtrl->getObjId())
	{
		onPlayerTeleport(playerCtrl, actorReviveHC.mapid(), MPVEC2WCoord(actorReviveHC.reviveposition()));
		playerCtrl->getLocoMotion()->setRotateRaw(actorReviveHC.reviveyaw());
		playerCtrl->getLocoMotion()->setRotatePitch(actorReviveHC.revivepitch());
		playerCtrl->PlayerControl::revive(actorReviveHC.revivetype());
	}
}

void MpGameSurviveNetHandler::handleActorTeleport2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActorTeleportHC actorTeleportHC;
	actorTeleportHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if(playerCtrl == NULL) return;
	if (actorTeleportHC.objid() == playerCtrl->getObjId())
	{
		WarningStringMsg("~handleActorTeleport2Client %lld", playerCtrl->getObjId());
		onPlayerTeleport(playerCtrl, actorTeleportHC.targetmap(), MPVEC2WCoord(actorTeleportHC.targetpos()));
	}
}

void MpGameSurviveNetHandler::handleActorMotion2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActorMotionHC actorMotionHC;
	actorMotionHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientActor *actor = objId2ActorOnClient(actorMotionHC.objid());

	if (actor != nullptr)
	{
		Rainbow::Vector3f &motion = actor->getLocoMotion()->m_Motion;
		/*
		motion.x += pkg.Body.ActorMotionHC.x;
		motion.y += pkg.Body.ActorMotionHC.y;
		motion.z += pkg.Body.ActorMotionHC.z;
		*/

		motion.x = actorMotionHC.x();
		motion.y = actorMotionHC.y();
		motion.z = actorMotionHC.z();
		if (actorMotionHC.has_ischangepos())
		{
			actor->getLocoMotion()->doMoveStep(motion);
		}
		actor->MotionHasChange();
	}
}

void MpGameSurviveNetHandler::handleMechaMotion2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_MechaMotionHC mechaMotionHC;
	mechaMotionHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ActorMechaUnit *actor = dynamic_cast<ActorMechaUnit *>(objId2ActorOnClient(mechaMotionHC.objid()));

	if (actor) actor->setMotionFromServer(mechaMotionHC.motiontype(), mechaMotionHC.motionparam());
}

void MpGameSurviveNetHandler::handlePlayerSkinning2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerSkinning playerSkinningHC;
	playerSkinningHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	
}

void MpGameSurviveNetHandler::handleActorAttrChange2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActorAttrChangeHC actorAttrChangeHC;
	actorAttrChangeHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientActor *actor = objId2ActorOnClient(actorAttrChangeHC.objid());
	if (actor == nullptr || actor->getAttrib() == nullptr) return;

	float dhp = actorAttrChangeHC.hp() - actor->getAttrib()->getHP();
	if (actorAttrChangeHC.hp() == 0) dhp -= 0.1f; //避免浮点误差: dhp + curhp != attr.HP
	actor->setBeHurtTargetID(actorAttrChangeHC.behurttarget());
	actor->getAttrib()->setExtraHP(actorAttrChangeHC.extarhp());
	actor->getAttrib()->addHP(dhp);
	if (actor->isPlayer()) {
		actor->getAttrib()->setOffLine(actorAttrChangeHC.offline());
	}
	ClientMob* sandMob = dynamic_cast<ClientMob*>(actor);
	if (sandMob != nullptr) {
		ActorSandworm* sandWorm = dynamic_cast<ActorSandworm*>(sandMob);
		if (sandWorm != nullptr && sandWorm->getAttrib() != nullptr &&
			sandWorm->m_Def != nullptr && sandWorm->GetLastHP() != sandWorm->getAttrib()->getHP()) {
			GameEventQue::GetInstance().postBossState(sandWorm->m_Def->ID, (int)sandWorm->getAttrib()->getHP());
		}
	}
	
}

void MpGameSurviveNetHandler::handleActorBuffChange2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActorBuffChangeHC actorBuffChangeHC;
	actorBuffChangeHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientActor *actor = objId2ActorOnClient(actorBuffChangeHC.objid());
	if (actor == nullptr) return;
	ActorLiving *living = dynamic_cast<ActorLiving *>(actor);
	if (living == nullptr) return;
	LivingAttrib *attrib = living->getLivingAttrib();

	if (actorBuffChangeHC.buffs_size() == 0)
	{
		attrib->clearBuff();
		return;
	}

	for (int i = 0; i<actorBuffChangeHC.buffs_size(); i++)
	{
		const PB_ActorBuff &actorBuff = actorBuffChangeHC.buffs(i);

		if (actorBuff.bufflv() > 0)
		{
			attrib->addBuff(actorBuff.buffid(), actorBuff.bufflv(), actorBuff.ticks(), actorBuff.buffinstanceid());
		}
		else
		{
			attrib->removeBuff(actorBuff.buffid(), true, actorBuff.buffinstanceid());
		}
	}
}

void MpGameSurviveNetHandler::handlePlayerAttrChange2Client(const PB_PACKDATA_CLIENT &pkg)
{
	//LOG_INFO("MpGameSurviveNetHandler::handlePlayerAttrChange2Client(): ");
	PB_PlayerAttrChangeHC playerAttrChangeHC;
	playerAttrChangeHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if(playerCtrl == NULL) return;
	PlayerAttrib *attrib = playerCtrl->getPlayerAttrib();
	if(attrib == NULL) return;

	//客机白天躺床上受到伤害起来	
	if (playerCtrl && playerCtrl->getWorld() && (playerAttrChangeHC.hp() - attrib->getHP() < 0))
	{
		World *pWorld = playerCtrl->getWorld();
		if (pWorld->isDaytime() && playerCtrl->isRestInBed())
		{
			playerCtrl->dismountActor();
		}
	}

	attrib->setExp(playerAttrChangeHC.exp());
	attrib->setOxygen(playerAttrChangeHC.oxygen());
	attrib->m_FoodLevel = (float)playerAttrChangeHC.foodlevel();

	if (playerAttrChangeHC.has_armor())
	{
		attrib->setArmor(playerAttrChangeHC.armor());
	}
	if (playerAttrChangeHC.has_maxhp())
	{
		attrib->setMaxHP(playerAttrChangeHC.maxhp());
	}
	if (playerAttrChangeHC.has_overflowhp())
	{
		attrib->setOverflowHP(playerAttrChangeHC.overflowhp());
	}

	if (playerAttrChangeHC.hp() < 0) // 主机血量<0了应该触发死亡
	{
		const PB_DieInfo& dieinfo = playerAttrChangeHC.dieinfo();
		SocAttackInfo& info = playerCtrl->getSocAttackInfo();
		info.atktype = (ATTACK_TYPE)dieinfo.atktype();
		info.survival_time = dieinfo.survival_time();
		info.buffId = dieinfo.buffid();
		info.buffLevel = dieinfo.bufflevel();
		info.toolid = dieinfo.toolid();
		info.playerid = dieinfo.playerid();
		info.length = dieinfo.length();
		info.mobid = dieinfo.mobid();

		//必须调用父类的kill
		playerCtrl->ClientActor::kill();
	}
	else 
	{
		attrib->addHPByTrueDamage(playerAttrChangeHC.hp() - attrib->getHP(), true); // 玩家
	}
	
	if (playerAttrChangeHC.has_maxstrength())
	{
		attrib->setBasicMaxStrength(playerAttrChangeHC.maxstrength());
	}
	if (playerAttrChangeHC.has_overflowstrength())
	{
		attrib->setBasicOverflowStrength(playerAttrChangeHC.overflowstrength());
	}
	if (playerAttrChangeHC.has_strength())
	{		
		attrib->setStrength(playerAttrChangeHC.strength());
	}

	if (playerAttrChangeHC.has_maxthirst())
	{
		attrib->setBasicMaxThirst(playerAttrChangeHC.maxthirst());
	}
	if (playerAttrChangeHC.has_thirst())
	{
		attrib->setThirst(playerAttrChangeHC.thirst());
	}

	if (playerAttrChangeHC.has_perseverance())
	{
		attrib->setPerseverance(playerAttrChangeHC.perseverance());
	}
	//LOG_INFO("MpGameSurviveNetHandler::handlePlayerAttrChange2Client(): %d = %s", playerCtrl->getUin(), attrib->toString().c_str());

	GetGameEventQue().postPlayerAttrChange();
}

void MpGameSurviveNetHandler::handleGameLeaderSwitch2Client(const PB_PACKDATA_CLIENT &pkg)
{
	MpGameSurvive* mp = dynamic_cast<MpGameSurvive*>(m_root);
	if (mp == nullptr)
	{
		return;
	}

	PB_GameLeaderSwitchHC gameLeaderSwitchHC;
	gameLeaderSwitchHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	int oldLeaderUin = mp->getGameLeaderUin();
	mp->setGameLeaderUin(gameLeaderSwitchHC.uin());

#ifndef IWORLD_SERVER_BUILD
	MINIW::ScriptVM::game()->callFunction("GameLeaderRefreshNotify", "ii", mp->getGameLeaderUin(), oldLeaderUin);
#endif
}

void MpGameSurviveNetHandler::handleActorAnim2Client(const PB_PACKDATA_CLIENT &pkg)//PB_ACTOR_ANIM_HC
{
	PB_ActorAnimHC actorAnimHC;
	actorAnimHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientActor *actor = objId2ActorOnClient(actorAnimHC.actorid());

	if (actor)
	{
		if (actorAnimHC.has_changeflag())
		{
			actor->resetAllFlags(actorAnimHC.changeflag());
		}
		if (isBoomerangItem(actor->GetItemId()))
		{
			ClientActorProjectile *projectile = static_cast<ClientActorProjectile *>(actor);
			if (projectile)
			{
				if (actorAnimHC.has_anim())
				{
					projectile->stopEntityModelAnim();
					projectile->playEntityModelAnim(actorAnimHC.anim(), 0);
				}
			}
			return;
		}
		ActorBody *body = actor->getBody();
		if (body == NULL) return;


		if ((actorAnimHC.has_actid() && actorAnimHC.actid() != body->getActID() && body->getActID() > 0)
			|| (actorAnimHC.has_actidtrigger() && actorAnimHC.actidtrigger() != body->getActTriggerID() && body->getActTriggerID() > 0
				|| actorAnimHC.has_animseq() && actorAnimHC.animseq() != body->getAnimSeq()))//20210923 codeby:chenwei 新增动作序列号判断
		{
			body->setCurAnim(-1, 0);
			actor->stopMotion(30000);
			body->stopAnim(SEQ_PLAY_ACT);
		}

	    if (actorAnimHC.has_actid() && actorAnimHC.actid() >= 0 && actorAnimHC.actid() != body->getActID())
		{
			auto def = GetDefManagerProxy()->getPlayActDef(actorAnimHC.actid());
			if (def)
			{
				//20210929 codeby:chenwei 新增装扮互动判定分支
				if (def->SkinID > 0 && def->SkinID2)//20210921 codeby:chenwei 播放装扮互动特效
				{
					body->playSkinActMotion(def->ID, 30000);
				}
				else
				{
					actor->stopMotion(30000);
					actor->playMotion(def->Effect.c_str(), 30000);
				}
			}
		}
		else if (actorAnimHC.has_actidtrigger() && actorAnimHC.actidtrigger() >= 0 && actorAnimHC.actidtrigger() != body->getActTriggerID())
		{
			auto def = GetDefManagerProxy()->getTriggerActDef(actorAnimHC.actidtrigger());
			if (def)
			{
				actor->stopMotion(30000);
				actor->playMotion(def->Effect.c_str(), 30000);
			}
		}
		//2021-09-14 codeby:chenwei 设置装扮互动副动作标记
		if (actorAnimHC.has_sideact())
		{
			body->setSideAct(actorAnimHC.sideact());
		}
		if (actorAnimHC.has_actid())
		{
			body->setAct(actorAnimHC.actid());
		}
		if (actorAnimHC.has_actidtrigger())
		{
			body->setActTrigger(actorAnimHC.actidtrigger());
		}
		//20210923 codeby:chenwei 新增动作序列号
		if (actorAnimHC.has_animseq())
		{
			body->setAnimSeq(actorAnimHC.animseq());
		}

		// 		if (actorAnimHC.customscale() > 0.0001)
		if (actorAnimHC.has_customscale())
		{
			actor->setCustomScale(actorAnimHC.customscale());
		}

		if (actorAnimHC.has_anim1() && actorAnimHC.anim1() == 127 && actorAnimHC.has_anim() && actorAnimHC.anim() >= 0)
		{
			if (actorAnimHC.has_isloop())
			{
				body->playAnim(actorAnimHC.anim(), actorAnimHC.isloop());
			}
			else
			{
				body->playAnim(actorAnimHC.anim());
			}
		}
		else
		{
			if (actorAnimHC.has_anim())
				body->setCurAnim(actorAnimHC.anim(), 0);
			if (actorAnimHC.has_anim1())
				body->setCurAnim(actorAnimHC.anim1(), 1);
		}

		if (actorAnimHC.has_anim())
		{
			int anim = actorAnimHC.anim();//客机看主机 魔炎变形还原 名字高度;
			//LOG_INFO("handleActorAnim2Client anim=%d, SEQ_RE_SHAPE_SHIFT=%d",anim, SEQ_RE_SHAPE_SHIFT);
			if (anim == SEQ_RE_SHAPE_SHIFT && body->getIsNeedRecoverShapeHeight())
			{
				//LOG_INFO("testShape:handleActorAnim2Client SEQ_RE_SHAPE_SHIFT: revoverShapeHeight");
				body->revoverShapeHeight();
			}
		}
		if (actorAnimHC.has_animweapon())
		{
			body->setCurAnimWeapon(actorAnimHC.animweapon(), 0);
		}
	}
}
void MpGameSurviveNetHandler::handleActorStopAnim2Client(const PB_PACKDATA_CLIENT &pkg) //PB_ACTOR_STOP_ANIM_HC
{
	PB_ActorStopAnimHC actorStopAnimHC;
	actorStopAnimHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientActor *actor = objId2ActorOnClient(actorStopAnimHC.actorid());

	if (actor)
	{
		ActorBody* body = actor->getBody();
		if (body == NULL) return;

		if (actorStopAnimHC.isseq())
		{
			if (body->getEntity())
				body->getEntity()->StopAnim(actorStopAnimHC.anim());
		}
		else
		{
			body->stopAnim(actorStopAnimHC.anim());
		}
	}
}

void MpGameSurviveNetHandler::handleChat2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ChatHC chatHC;
	chatHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	const char *extend = "";
	//2021-12-20 codeby: wangyang 会员聊天气泡
	int bubble_id = 0;
	std::string extend_data = "";
	if (chatHC.has_extend())
	{
		extend = chatHC.extend().c_str();
		//2021-12-20 codeby: wangyang 会员聊天气泡
		jsonxx::Object jsonObj;
		bool bParseRet = jsonObj.parse((const char*)extend);
		if (bParseRet)
		{
			if (jsonObj.has<jsonxx::Number>("bubble"))
			{
				bubble_id = (int)jsonObj.get<jsonxx::Number>("bubble");
			}

			if (jsonObj.has<jsonxx::String>("extend_data"))
			{
				extend_data = jsonObj.get<jsonxx::String>("extend_data");
			}
		}
	}

	const char* tmpContent = chatHC.content().c_str();
#ifdef IWORLD_UNIVERSE_BUILD
	if (chatHC.translate().compare("") != 0)
	{
		tmpContent = chatHC.translate().c_str();
	}
#endif 
	GetGameEventQue().postChatEvent(chatHC.chattype(), chatHC.speaker().c_str(), tmpContent, chatHC.uin(), chatHC.language(), extend);

	auto chat = MNSandbox::GetCurrentChatRoot().ToCast<MNSandbox::SandboxChatService>();
	if (chat && chatHC.chattype() == 0)
	{
		chat->GetNotifyHostToClientContent()->Emit(chatHC.uin(), chatHC.content());
	}

	bool isDynamicEmoji = false;
	if (!extend_data.empty())
		MINIW::ScriptVM::game()->callFunction("Is_Dynamic_Emoji", "s>b", extend_data.c_str(), &isDynamicEmoji);

	if (isDynamicEmoji) return; // 暂不支持

	//音乐厅聊天气泡 2021-09-25 codeby: luoshuai
	ClientPlayer *player = uin2Player(chatHC.uin());
	if (chatHC.chattype() == 0 && player)
	{
		//2021-12-20 codeby: wangyang 会员聊天气泡
		player->tickNewChat((chatHC.content()).c_str(), bubble_id);
	}
}

//20210914装扮互动邀请消息推送 cody-by: wangyu
void MpGameSurviveNetHandler::handleActorInvite2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActorInviteHC actorInviteHC;
	actorInviteHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	GetGameEventQue().postActorInviteEvent(actorInviteHC.invitetype(), actorInviteHC.targetuin(), actorInviteHC.actid(), actorInviteHC.inviterposx(), actorInviteHC.inviterposz());
}


void MpGameSurviveNetHandler::handleYMVoice2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_YMVoiceHC yMVoiceHC;
	yMVoiceHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int uin = yMVoiceHC.uin();
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl && uin == playerCtrl->getUin())
	{
		// 客机此处不处理主机对自己的同步, 防止生成playerBriefInfo 2022.03.11 by 黄林(<EMAIL>)
		return;
	}

	PlayerBriefInfo* dest = NULL;
	MpGameSurvive* mp = dynamic_cast<MpGameSurvive*>(m_root);
	if (mp)
	{
		dest = mp->findPlayerInfoByUin(uin);
		if (!dest)
		{
			dest = mp->addPlayerBriefInfo(uin);
		}
	}
	assert(dest);
	
	dest->YMmemberid = yMVoiceHC.ymmemberid();
	dest->YMspeakerswitch = yMVoiceHC.speakerswitch();
	dest->YMmicswitch = yMVoiceHC.micswitch();
	dest->YMmemberrole = yMVoiceHC.ymmemberrole();

	MINIW::ScriptVM::game()->callFunction("VoiceInfoChange", "");
}

void MpGameSurviveNetHandler::handleSkillCD2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_SkillCDHC skillCDHC;
	skillCDHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl)
	{
		MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->setItemCdForTrigger(playerCtrl->getUin(), skillCDHC.itemid(), skillCDHC.cd());
		playerCtrl->setSkillCD(skillCDHC.itemid(), skillCDHC.cd());
	}
}

void MpGameSurviveNetHandler::handleHorseSkillCD2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_Horse_SkillCDHC skillCDHC;
	skillCDHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientActor *actor = objId2ActorOnClient(skillCDHC.actorid());
	if (actor)
	{
		ActorHorse* horse = dynamic_cast<ActorHorse*>(actor);
		if (horse)
			horse->setSkillCD(skillCDHC.index(), skillCDHC.cd());

		/*if(actor->getObjType() == OBJ_TYPE_SHAPESHIFT_HORSE)
		{
			ActorShapeShiftHorse* horse = dynamic_cast<ActorShapeShiftHorse*>(actor);
			if (horse)
				horse->setSkillCD(skillCDHC.index(), skillCDHC.cd());
		}
		else if(actor->getObjType() == OBJ_TYPE_DRAGON_MOUNT)
		{
			ActorDragonMount* dragonMount = dynamic_cast<ActorDragonMount*>(actor);
			if(dragonMount)
			{
				dragonMount->setSkillCD(skillCDHC.index(), skillCDHC.cd());
			}
		}
		else if(actor->getObjType() == OBJ_TYPE_MOON_MOUNT)
		{
			ActorMoonMount* moonMount = dynamic_cast<ActorMoonMount*>(actor);
			if(moonMount)
			{
				moonMount->setSkillCD(skillCDHC.index(), skillCDHC.cd());
			}
		}
		else if (actor->getObjType() == OBJ_TYPE_DOUDU_MOUNT)
		{
			ActorDouDuMount* douDuMount = dynamic_cast<ActorDouDuMount*>(actor);
			if (douDuMount)
			{
				douDuMount->setSkillCD(skillCDHC.index(), skillCDHC.cd());
			}
		}
		else if (actor->getObjType() == OBJ_TYPE_PUMPKIN_HORSE)
		{
			ActorPumpkinHorse* horse = dynamic_cast<ActorPumpkinHorse*>(actor);
			if (horse)
			{
				horse->setSkillCD(skillCDHC.index(), skillCDHC.cd());
			}
		}*/
	}
}

void MpGameSurviveNetHandler::handleWGlobalUpdate2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_WGlobalUpdateHC wGlobalUpdateHC;
	wGlobalUpdateHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	
	WorldManager *worldMgr = m_root->getWorldMgr();
	if(worldMgr){
		worldMgr->setWorldTime(wGlobalUpdateHC.worldtime());
		worldMgr->setDayTime(wGlobalUpdateHC.daynighttime());
		World *pworld = worldMgr->getWorld(wGlobalUpdateHC.mapid());
		if (pworld && pworld->m_Environ)
		{
			pworld->m_Environ->setRainingByServer(wGlobalUpdateHC.raining() != 0);
			pworld->m_Environ->setDarkingByServer(wGlobalUpdateHC.darking() != 0);
			if(wGlobalUpdateHC.has_curweather())
				pworld->m_Environ->setCurWeather(wGlobalUpdateHC.curweather());
		}
	}	
}
void MpGameSurviveNetHandler::handleVaCantBossStateClient(const PB_PACKDATA_CLIENT &pkg)
{
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL) return;
	if (playerCtrl->getWorld() == NULL) return;
	PB_VacantBossStateHC vacantBossStateHC;
	vacantBossStateHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	int type = vacantBossStateHC.type();
	//任务
	if (VACANT_EVENT::EVE_MISSION == type)
	{
		GetGameEventQue().postMissionComplete(vacantBossStateHC.ival0());
	}
	//对白
	if (VACANT_EVENT::EVE_DIALOGUE == type)
	{
		GetGameEventQue().postGameDialogue(vacantBossStateHC.ival0());
	}
	//召唤时间
	if (VACANT_EVENT::EVE_ENTER_TIME == type)
	{
		//生物(mob)
		ClientActor *actor = objId2ActorOnClient(vacantBossStateHC.objid());
		if (actor == NULL)
		{
			return;
		}
		ActorDragon* dragon = dynamic_cast<ActorDragon*>(actor);
		if (dragon)
		{
			dragon->setTimestep(vacantBossStateHC.fval0());
		}
		ActorGiant* giant = dynamic_cast<ActorGiant*>(actor);
		if (giant)
		{
			giant->setTimestep(vacantBossStateHC.fval0());
		}
	}
	if (VACANT_EVENT::EVE_BOSS_POS == type)
	{
		ChunkGenNormal* gen = dynamic_cast<ChunkGenNormal*>(playerCtrl->getWorld()->getChunkProvider());
		WCoord targetPos = MPVEC2WCoord(vacantBossStateHC.targetpos());
		if (targetPos.isZero())
		{
			GetWorldManagerPtr()->removeBossPoint(playerCtrl->getWorld()->getCurMapID());
		}
		if (gen)
		{
			gen->setBossPos(targetPos);
		}
	}
}

void MpGameSurviveNetHandler::handleHomeLandMenuBuySuccess2Client(const PB_PACKDATA_CLIENT &pkg)
{
	MINIW::ScriptVM::game()->callFunction("FreshHomelandMenuData", NULL);
}

/*
	20210724:特惠家具购买同步刷新状态 codeby：yangzhenyu
*/
void MpGameSurviveNetHandler::handleHomeLandSpecialFurnitureBuySuccess2Client(const PB_PACKDATA_CLIENT &pkg)
{
	MINIW::ScriptVM::game()->callFunction("FreshHomelandSpecialFurnitureData", NULL);
}

void MpGameSurviveNetHandler::handleHomeLandShopCell2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_HomeLandShopCellCH shopcellCH;
	shopcellCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	MINIW::ScriptVM::game()->callFunction("FreshHomelandFarmshopSellData", "iii",shopcellCH.uin(),shopcellCH.itemid(),shopcellCH.num());
}

static bool IsSameCustomSkin(const char *p1, const char *p2)
{
	if(p1==NULL || p2==NULL) return p1==p2;
	else return strcmp(p1, p2)==0;
}

void MpGameSurviveNetHandler::handlePlayerInfoUpdate2Client(const PB_PACKDATA_CLIENT &pkg)
{
	MpGameSurvive*mp = dynamic_cast<MpGameSurvive*>(m_root);
	WorldManager *worldMgr = m_root->getWorldMgr();
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	PB_PlayersUpdateInfoHC playersUpdateInfoHC;
	playersUpdateInfoHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (playersUpdateInfoHC.teamscores_size() > 0)
	{ // 旧的处理方式, 用teamscores_size来判断是否为优化后的协议
		int lastNum = 0;
		if (mp)
		{
			lastNum = mp->getNumPlayerBriefInfo();
		}
		
		// 2022-01-18 codeby:liusijia 如果未设置mapid 则认为仅是更新属性，不做最后的删除操作
		bool refreshAll = true;   
		std::vector<PlayerBriefInfo *>existinfo;
		for (int i = 0; i<playersUpdateInfoHC.players_size(); i++)
		{
			const PB_PlayerBriefInfo &src = playersUpdateInfoHC.players(i);
			int srcUin = src.uin();
			if (ROOM_SERVER_RENT == GetGameInfoProxy()->GetRoomHostType())
			{

				SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_changeToRealHost",
					SandboxContext(nullptr).SetData_Number("uin", srcUin));
				if (result.IsExecSuccessed())
				{
					srcUin = (int)result.GetData_Number();
				}
			}
			// 放在 playerCtrl 处理之前，避免被continue
			if (!src.has_mapid())
				refreshAll = false;

			if (playerCtrl && srcUin == playerCtrl->getUin())
			{
				if (src.has_teamid()){
					int teamid = playerCtrl->getTeam();
					if (src.teamid() != teamid)
					{
						playerCtrl->setTeam(src.teamid());
						if (teamid > 0)
							MINIW::ScriptVM::game()->callFunction("OnChangeTeam", "");
					}
				}
				if (src.cgvars_size()>0)
				{
					playerCtrl->setGameScore(src.cgvars(0) & 0xffffff);
					playerCtrl->setGameRanking(src.cgvars(0) >> 24);
					playerCtrl->setGameResults(src.cgvars(1) & 3);
				}
				continue;
			}
			
			PlayerBriefInfo *dest = NULL;
			if (mp)
			{
				dest = mp->findPlayerInfoByUin(srcUin);
				if (!dest)
				{
					#ifdef IWORLD_SERVER_BUILD
					// 云服的BriefInfo只在客户端到服务器的过程中生成, 此处不应调用addPlayerBriefInfo
					continue;
					#endif
					dest = mp->addPlayerBriefInfo(srcUin);
				} 
			}

			if (!dest)
			{
				assert(false);
				return;
			}

			if (src.has_hp())
				dest->hp = src.hp();
			if (src.has_maxhp())
			{
				dest->maxhp = src.maxhp();
			}
			if (src.has_overflowhp())
			{
				dest->overflowhp = src.overflowhp();
			}
			if (src.has_strength())
			{
				dest->strength = src.strength();
			}
			if (src.has_maxstrength())
			{
				dest->maxstrength = src.maxstrength();
			}
			if (src.has_overflowstrength())
			{
				dest->overflowstrength = src.overflowstrength();
			}
			if (src.has_armor())
			{
				dest->armor = src.armor();
			}
			if (src.has_perseverance())
			{
				dest->perseverance = src.perseverance();
			}

			if (src.has_mapid())
				dest->mapid = src.mapid();
			if (src.has_pos()){
				dest->x = src.pos().x();
				dest->y = src.pos().y();
				dest->z = src.pos().z();
			}
			if (src.has_teamid())
				dest->teamid = src.teamid();
			
			if (src.has_vipinfo()){
				dest->vipinfo.vipType = src.vipinfo().viptype();
				dest->vipinfo.vipLevel = src.vipinfo().viplevel();
				dest->vipinfo.vipExp = src.vipinfo().vipexp();
			}
			if (src.has_inspectator())
				dest->inSpectator = src.inspectator();
			if (src.has_acctountskinid())
				dest->accountSkinID = src.acctountskinid();

			for (int j = 0; j<src.cgvars_size(); j++) dest->cgamevar[j] = src.cgvars(j);

			if (src.has_nickname() && src.nickname().length() > 0)
			{
				MyStringCpy(dest->nickname, sizeof(dest->nickname), src.nickname().c_str());
				GetDefManagerProxy()->filterStringDirect(dest->nickname);
			}
			if (src.has_customjson()) MyStringCpy(dest->customjson, sizeof(dest->customjson), src.customjson().c_str());

			if (src.has_playerindex() && src.playerindex()>0)
			{
				dest->model = PlayerIndex2Model(src.playerindex());
				dest->geniuslv = PlayerIndex2Genius(src.playerindex());
				dest->skinid = PlayerIndex2Skin(src.playerindex());
			}
			if (src.has_frameid())
			{
				dest->frameid = src.frameid();
			}

			if (src.has_exposepostoother())
			{
				dest->exposePosToOther = src.exposepostoother();
			}

			if (worldMgr)
			{
				ClientPlayer *player = static_cast<ClientPlayer*>(worldMgr->getPlayerByUin(srcUin));
				if (player){
					PlayerAttrib* playerAttrib = player->getPlayerAttrib();
					if (dest->teamid != player->getTeam()) player->setTeam(dest->teamid);
					//设置游戏中玩家的血量值
					if (playerAttrib && worldMgr->isRemote()){
						if (dest->maxhp > 0) playerAttrib->setMaxHP(dest->maxhp);   
						playerAttrib->setOverflowHP(dest->overflowhp);   
						if (dest->hp > 0) playerAttrib->setHP(dest->hp, true);   
						playerAttrib->setMaxStrength(dest->maxstrength);   
						playerAttrib->setOverflowStrength(dest->overflowstrength); 
						playerAttrib->setStrength(dest->strength);
						playerAttrib->setMaxThirst(dest->maxthirst);
						//playerAttrib->setOverflowThirst(dest->overflowthirst);
						playerAttrib->setThirst(dest->thirst);
						playerAttrib->setArmor(dest->armor);
						playerAttrib->setPerseverance(dest->perseverance);
						LOG_INFO("MpGameSurviveNetHandler::handlePlayerInfoUpdate2Client(): %d = ", srcUin);   
					}
					player->setVipInfo(dest->vipinfo);
					MINIW::ScriptVM::game()->callFunction("ShowGameScenePlayerAchieve", "i", srcUin); //显示成就信息
					player->setGVoiceInfo(dest->YMspeakerswitch);
					player->SetExposePosToOther(dest->exposePosToOther);
				}
			}

			existinfo.push_back(dest);
		}

	#ifndef IWORLD_SERVER_BUILD
		if (worldMgr && worldMgr->isGameMakerRunMode() )
		{
			GameMode *rulemgr = static_cast<GameMode*>(worldMgr->m_RuleMgr);
			if (rulemgr)
			{
				int maxlifenum = int(rulemgr->getRuleOptionVal(GMRULE_LIFE_NUM));

				for (int i = 0; i < playersUpdateInfoHC.teamscores_size(); i++)
				{
					rulemgr->setTeamScore(i, playersUpdateInfoHC.teamscores(i));
					rulemgr->setTeamResults(i, playersUpdateInfoHC.teamflags(i) & 3);
					if (maxlifenum > 0) rulemgr->setTeamDieTimes(i, (playersUpdateInfoHC.teamflags(i) >> 2) & 0xff);
				}

				int pSzie = playersUpdateInfoHC.players_size();
				int pNum = 0;
				if (mp)
				{
					pNum = mp->getNumPlayerBriefInfo();
				}
					
				if (rulemgr->getGameStage() < CGAME_STAGE_COUNTDOWN && ((pSzie != pNum + 1) || (pNum != lastNum)))
				{	
					MINIW::ScriptVM::game()->callFunction("OnChangeNumOfPlayers", "i", pSzie);
				}
			}
		}
	#endif
		if (mp && refreshAll)
		{
			mp->clearPlayerBriefInfo(existinfo);
		}
		
	} else{
		bool addBriefInfo = false;
		for (int i = 0; i<playersUpdateInfoHC.players_size(); i++)
		{
			const PB_PlayerBriefInfo &src = playersUpdateInfoHC.players(i);
			int srcUin = src.uin();
			/* 下方changeToRealHost未做任何处理, 暂时屏蔽 2022.02.17 by huanglin
			if (ROOM_SERVER_RENT == GetClientInfoProxy()->getRoomHostType())
				srcUin = PermitsManager::GetInstance().changeToRealHost(srcUin);
			*/

			if (playerCtrl && srcUin == playerCtrl->getUin())
			{
				continue;
			}
			
			PlayerBriefInfo *dest = NULL;
			if (mp)
			{
				dest = mp->findPlayerInfoByUin(srcUin);
				if (!dest)
				{
					#ifdef IWORLD_SERVER_BUILD
					// 云服的BriefInfo只在客户端到服务器的过程中生成, 此处不应调用addPlayerBriefInfo
					continue;
					#endif
					dest = mp->addPlayerBriefInfo(srcUin);
					addBriefInfo = true;
				} 
			}

			if (!dest)
			{
				assert(false);
				return;
			}
			IClientPlayer* iplayer = worldMgr ? worldMgr->getPlayerByUin(srcUin) : NULL;
			ClientPlayer* player = iplayer ? iplayer->GetPlayer() : nullptr;
			PlayerAttrib* playerAttrib = player ? player->getPlayerAttrib(): NULL;
			//if (src.has_maxhp())
			//{
			//	dest->maxhp = src.maxhp();
			//	if (playerAttrib && (dest->maxhp > 0))
			//		playerAttrib->setMaxHP(dest->maxhp);   
			//}
			if (src.has_hp()){
				dest->hp = src.hp();
				if (playerAttrib && (dest->hp > 0))
					playerAttrib->setHP(dest->hp, true);
			}
			//if (src.has_overflowhp())
			//{
			//	dest->overflowhp = src.overflowhp();
			//	if (playerAttrib)
			//		playerAttrib->setOverflowHP(dest->overflowhp);
			//}
			//if (src.has_strength())
			//{
			//	dest->strength = src.strength();
			//	if (playerAttrib)
			//		playerAttrib->setStrength(dest->strength);
			//}
			//if (src.has_maxstrength())
			//{
			//	dest->maxstrength = src.maxstrength();
			//	if (playerAttrib)
			//		playerAttrib->setMaxStrength(dest->maxstrength);
			//}
			//if (src.has_overflowstrength())
			//{
			//	dest->overflowstrength = src.overflowstrength();
			//	if (playerAttrib)
			//		playerAttrib->setOverflowStrength(dest->overflowstrength); 
			//}

			if (src.has_thirst())
			{
				dest->thirst = src.thirst();
				if (playerAttrib)
					playerAttrib->setStrength(dest->strength);
			}
			if (src.has_maxthirst())
			{
				dest->maxthirst = src.maxstrength();
				if (playerAttrib)
					playerAttrib->setMaxStrength(dest->maxthirst);
			}

			if (src.has_mapid())
				dest->mapid = src.mapid();
			if (src.has_pos()){
				dest->x = src.pos().x();
				dest->y = src.pos().y();
				dest->z = src.pos().z();
			}
			if (src.has_teamid()){
				dest->teamid = src.teamid();
				if (player && (dest->teamid != player->getTeam()))
					player->setTeam(dest->teamid);
			}
			if (src.has_vipinfo()){
				dest->vipinfo.vipType = src.vipinfo().viptype();
				dest->vipinfo.vipLevel = src.vipinfo().viplevel();
				dest->vipinfo.vipExp = src.vipinfo().vipexp();
				if (player)
					player->setVipInfo(dest->vipinfo);
			}
			if (src.has_inspectator())
				dest->inSpectator = src.inspectator();
			if (src.has_acctountskinid())
				dest->accountSkinID = src.acctountskinid();

			for (int j = 0; j<src.cgvars_size(); j++)
				dest->cgamevar[j] = src.cgvars(j);

			if (src.has_nickname() && src.nickname().length() > 0)
			{
				MyStringCpy(dest->nickname, sizeof(dest->nickname), src.nickname().c_str());
				GetDefManagerProxy()->filterStringDirect(dest->nickname);
			}
			if (src.has_customjson())
				MyStringCpy(dest->customjson, sizeof(dest->customjson), src.customjson().c_str());

			if (src.has_playerindex() && src.playerindex()>0)
			{
				dest->model = PlayerIndex2Model(src.playerindex());
				dest->geniuslv = PlayerIndex2Genius(src.playerindex());
				dest->skinid = PlayerIndex2Skin(src.playerindex());
			}
			if (src.has_frameid())
			{
				dest->frameid = src.frameid();
			}
			if (src.has_exposepostoother())
			{
				dest->exposePosToOther = src.exposepostoother();
			}
		}
		
		#ifndef IWORLD_SERVER_BUILD
		// 准备阶段界面展示玩家数量增加 2022.04.12 by huanglin
		if (addBriefInfo){
			if (worldMgr && worldMgr->isGameMakerRunMode()){
				GameMode *rulemgr = static_cast<GameMode*>(worldMgr->m_RuleMgr);
				if (rulemgr && rulemgr->getGameStage() < CGAME_STAGE_COUNTDOWN)
				{
					MINIW::ScriptVM::game()->callFunction("OnChangeNumOfPlayers", "i", mp->getNumPlayers());
				}
			}
		}
		#endif
	}
}

void MpGameSurviveNetHandler::handlePlayerLeave2Client(const PB_PACKDATA_CLIENT &pkg)
{
	MpGameSurvive*mp = dynamic_cast<MpGameSurvive*>(m_root);
	if (mp){
		PB_PlayerLeaveHC playersLeaveHC;
		playersLeaveHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

		std::set<int> toClearUins;
		for (int i=0; i < playersLeaveHC.uins_size(); ++i)
		{
			toClearUins.insert(playersLeaveHC.uins(i));
			MINIW::ScriptVM::game()->callFunction("OnPlayerLeave", "i", playersLeaveHC.uins(i));
		}
		if (toClearUins.size() > 0){
			WorldManager* worldMgr = m_root->getWorldMgr();
			GameMode *rulemgr = worldMgr? static_cast<GameMode*>(worldMgr->m_RuleMgr): NULL;

			// rulemgr存在时说明worldMgr不为空
			if (rulemgr && worldMgr->isGameMakerRunMode())
			{
				int cleared = 0;
				if (rulemgr->getGameStage() != CGAME_STAGE_END) //结算时不清理briefinfo
					cleared = mp->clearPlayerBriefInfoByUin(toClearUins);
				if ((rulemgr->getGameStage() < CGAME_STAGE_COUNTDOWN) && cleared)
				{
					MINIW::ScriptVM::game()->callFunction("OnChangeNumOfPlayers", "i", mp->getNumPlayers());
				}
			} else {
				mp->clearPlayerBriefInfoByUin(toClearUins);
				MINIW::ScriptVM::game()->callFunction("OnChangeNumOfPlayers", "i", mp->getNumPlayers());
			}
		}
	}
}
void MpGameSurviveNetHandler::handleSyncTeamScore2Client(const PB_PACKDATA_CLIENT &pkg)
{
	WorldManager *worldMgr = m_root->getWorldMgr();
	if (worldMgr && worldMgr->isGameMakerRunMode() )
	{
		GameMode *rulemgr = static_cast<GameMode*>(worldMgr->m_RuleMgr);
		if (rulemgr){
			PB_TeamScoreHC TeamScoresHC;
			TeamScoresHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
			int maxlifenum = int(rulemgr->getRuleOptionVal(GMRULE_LIFE_NUM));

			for (int i = 0; i < TeamScoresHC.teams_size(); i++)
			{
				auto & team = TeamScoresHC.teams(i);
				auto team_id = team.teamid();
				rulemgr->setTeamScore(team_id, team.score());
				rulemgr->setTeamResults(team_id, team.flags() & 3);
				if (maxlifenum > 0)
					rulemgr->setTeamDieTimes(team_id, (team.flags() >> 2) & 0xff);
			}
		}
	}
}
void MpGameSurviveNetHandler::handleSetTeamID2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (!playerCtrl)
		return;
	PB_SetTeamIDHC setTeamIDHC;
	setTeamIDHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	
	unsigned long long objid = setTeamIDHC.has_objid()? setTeamIDHC.objid(): 0;
	if (objid == 0 || objid == playerCtrl->getUin())
	{
		int teamid = playerCtrl->getTeam();
		if (setTeamIDHC.teamid() != teamid)
		{
			playerCtrl->setTeam(setTeamIDHC.teamid());
			if (teamid > 0)
				MINIW::ScriptVM::game()->callFunction("OnChangeTeam", "");
		}
	}
	else
	{
		ActorLiving *actor = dynamic_cast<ActorLiving *>(objId2ActorOnClient(objid));
		if (actor)
		{
			actor->setTeam(setTeamIDHC.teamid());
		}
	}
}
void MpGameSurviveNetHandler::handleSetPlayerGameInfo2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_SetPlayerGameInfoHC setInfoHC;;
	setInfoHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (!playerCtrl)
		return;

	if (setInfoHC.has_score())
		playerCtrl->setGameScore(setInfoHC.score());
	if (setInfoHC.has_ranking())
		playerCtrl->setGameRanking(setInfoHC.ranking());
	if (setInfoHC.has_result())
		playerCtrl->setGameResults(setInfoHC.result());
	if (setInfoHC.has_playerresult())
		playerCtrl->setGameResults(setInfoHC.playerresult());
}

void MpGameSurviveNetHandler::handleCGameStage2Client(const PB_PACKDATA_CLIENT &pkg)
{
	WorldManager *worldMgr = m_root->getWorldMgr();
	PB_CGameStageHC cGameStageHC;
	cGameStageHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	CGAME_STAGE_TYPE stageType = (CGAME_STAGE_TYPE)cGameStageHC.stage();

	// 过滤掉开局介绍和队伍选择，这两个依赖于本地逻辑，不需要同步
	if (stageType == CGAME_STAGE_SHOWINTROS || stageType == CGAME_STAGE_SELECTTEAM)
		return;

	if (worldMgr && worldMgr->m_RuleMgr)
		worldMgr->m_RuleMgr->setCustomGameStage(stageType, cGameStageHC.gametime());
}

void MpGameSurviveNetHandler::handlePlayerPermits2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	PB_PlayerPermitHC playerPermitHC;
	playerPermitHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PermitsSubSystem* permitsModule = GET_SUB_SYSTEM(PermitsSubSystem);
	if (permitsModule)
	{
		permitsModule->onReset(false, &playerPermitHC);
	}
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_onReset",
		SandboxContext(nullptr).SetData_Bool("ishost", false).SetData_Usertype<game::hc::PB_PlayerPermitHC>("playerPermitHC", &playerPermitHC));

	if (playerCtrl)
	{
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_setPlayerPermits",
			SandboxContext(nullptr).SetData_Number("uin", playerCtrl->getUin()).SetData_Number("mode", playerPermitHC.playerflags()));
	}

	if (ROOM_SERVER_RENT == GetClientInfoProxy()->getRoomHostType())
	{
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_setSpamPreventionMinutes",
			SandboxContext(nullptr).SetData_Number("minute", playerPermitHC.spampreventionminutes()));
	}

	if (playerCtrl) MINIW::ScriptVM::game()->callFunction("OnChangePermits", "i",playerCtrl->getUin());
}

void MpGameSurviveNetHandler::handleGameTips2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_GameTipsHC gameTipsHC;
	gameTipsHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl) 
	{
		std::string tempName = gameTipsHC.othername();
		if (gameTipsHC.has_translatename() && gameTipsHC.translatename().compare("") != 0)
		{
			tempName = gameTipsHC.translatename();
		}
		playerCtrl->notifyGameInfo2Self(gameTipsHC.tipstype(), gameTipsHC.id(), gameTipsHC.num(), tempName.c_str());
	}
}

void MpGameSurviveNetHandler::handleAccountHorse2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_AccountHorseHC accountHorseHC;
	accountHorseHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl)
	{
		if (accountHorseHC.synctype() == ACCHORSE_SYNC_LIVETICKS)
			playerCtrl->resetAccountHorseLiveTick(accountHorseHC.horseid(), accountHorseHC.syncdata());
	}
}

void MpGameSurviveNetHandler::handlePlayWeaponEffectClient(const PB_PACKDATA_CLIENT &pkg)
{
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if(playerCtrl == NULL) return;
	if(playerCtrl->getWorld() == NULL) return;

	PB_PlayWeaponEffectHC playWeaponEffectHC;
	playWeaponEffectHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientActorMgr *actormgr = playerCtrl->getWorld()->getActorMgr()->ToCastMgr();
	ClientActor *actor = actormgr->findActorByWID(playWeaponEffectHC.objid());
	if (actor && actor->getBody())
	{
		if (playWeaponEffectHC.effectstatus() == 0)
			actor->getBody()->playWeaponMotion(playWeaponEffectHC.effectname().c_str(), true, playWeaponEffectHC.effectid(), (float)playWeaponEffectHC.effectscale() / 100.0f);
		else
			actor->getBody()->stopWeaponMotion(playWeaponEffectHC.effectid());
	}
}

void MpGameSurviveNetHandler::handleScriptVars2Client(const PB_PACKDATA_CLIENT &pkg)
{
	WorldManager *worldMgr = m_root->getWorldMgr();
	if (worldMgr == NULL) return;

	PB_ScriptVarHC scriptVarHC;
	scriptVarHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	for (int i = 0; i<scriptVarHC.scriptvars_size(); i++)
	{
		MNSandbox::GetGlobalEvent().Emit<int,float>("OnlineService_setScriptVar", i, scriptVarHC.scriptvars(i));
	}
}

void MpGameSurviveNetHandler::handlePlayEffect2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL || playerCtrl->getWorld() == NULL) return;

	PB_PlayEffectHC playEffectHC;
	playEffectHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (playerCtrl == NULL) return;
	if (playerCtrl->getWorld() == NULL) return;
	EffectManager* effectmgr = playerCtrl->getWorld()->getEffectMgr();
	ClientActorMgr* actormgr = playerCtrl->getWorld()->getActorMgr()->ToCastMgr();

	if (playEffectHC.effecttype() == PB_EFFECT_PARTICLE)
	{
		const PB_EffectParticle& pt = playEffectHC.particle();
		if (effectmgr)
		{
			if (playEffectHC.has_effectscale())
			{
				//触发器
				int lifeTime = pt.has_age() ? pt.age() : 0;
				effectmgr->playParticleEffectForTrigger(pt.name().c_str(), MPVEC2WCoord(pt.pos()), playEffectHC.effectscale() / 1000.0f, lifeTime);
			}
			else
			{
				if (pt.has_ziprespath())
				{
					char zipPath[256] = { 0 };
					sprintf(zipPath, "%s", pt.ziprespath().c_str());
					effectmgr->playParticleEffectAsync(pt.name().c_str(), MPVEC2WCoord(pt.pos()), pt.age(), pt.yaw(), pt.pitch(), false, 0, 0, Rainbow::ColorQuad(pt.color()), zipPath);
				}
				else
				{
					effectmgr->playParticleEffectAsync(pt.name().c_str(), MPVEC2WCoord(pt.pos()), pt.age(), pt.yaw(), pt.pitch(), false, 0, 0, Rainbow::ColorQuad(pt.color()));
				}
			}
		}
	}
	else if (playEffectHC.effecttype() == PB_EFFECT_PARTICLEID)
	{
		if (effectmgr)
		{
			const PB_EffectParticleID& pt = playEffectHC.particleid();
			auto effectpath = GetDefManagerProxy()->GetParticlesStrDefCsvIdMap().find(pt.id());
			if (effectpath != GetDefManagerProxy()->GetParticlesStrDefCsvIdMap().end())
			{
				if (playEffectHC.has_effectscale())
				{
					//触发器
					int lifeTime = pt.has_age() ? pt.age() : 0;
					effectmgr->playParticleEffectForTrigger(effectpath->second.c_str(), MPVEC2WCoord(pt.pos()), playEffectHC.effectscale() / 1000.0f, lifeTime);
				}
				else
				{
					float yaw = 0, pitch = 0;
					if (pt.has_yaw())
						yaw = pt.yaw() / 100.0f;
					if (pt.has_pitch())
						pitch = pt.pitch() / 100.0f;
					if (pt.has_ziprespath())
					{
						char zipPath[256] = { 0 };
						sprintf(zipPath, "%s", pt.ziprespath().c_str());
						effectmgr->playParticleEffectAsync(effectpath->second.c_str(), MPVEC2WCoord(pt.pos()), pt.age(),
							yaw, pitch, false, 0, 0, Rainbow::ColorQuad(pt.color()), zipPath);
					}
					else
					{
						effectmgr->playParticleEffectAsync(effectpath->second.c_str(), MPVEC2WCoord(pt.pos()), pt.age(),
							yaw, pitch, false, 0, 0, Rainbow::ColorQuad(pt.color()));
					}
				}
			}
		}
	}
	else if (playEffectHC.effecttype() == PB_EFFECT_STOPPARTICLE)
	{
		const PB_EffectParticle& pt = playEffectHC.particle();
		if (effectmgr)
		{
			if (effectmgr->getParticleOnPos(MPVEC2WCoord(pt.pos()), pt.name().c_str()) != NULL)
			{
				effectmgr->stopParticleEffect(pt.name().c_str(), MPVEC2WCoord(pt.pos()));
			}
		}
	}
	else if (playEffectHC.effecttype() == PB_EFFECT_PICKITEM)
	{
		if (actormgr == NULL) return;
		const PB_EffectPickItem& pi = playEffectHC.pickitem();
		ClientPlayer* player = actormgr->findPlayerByUin(int(pi.pickerobj()));
		ClientActor* item = actormgr->findActorByWID(pi.itemobj());

		if (player && item)
		{
			if (effectmgr) effectmgr->GetGameEffectMgr()->playPickItemEffect(player, item, pi.yoffset());
		}
	}
	else if (playEffectHC.effecttype() == PB_EFFECT_SOUND)
	{
		const PB_EffectSound& es = playEffectHC.sound();

		if (effectmgr) effectmgr->playSound(MPVEC2WCoord(es.pos()), es.name().c_str(), es.volume(), es.pitch(), es.flags() & ~PLAYSND_SYNC, es.segment() - 1, es.fixpitch());
	}
	else if (playEffectHC.effecttype() == PB_EFFECT_SOUNDID)
	{
		if (effectmgr)
		{
			const PB_EffectSoundID& es = playEffectHC.soundid();

			auto soundname = GetDefManagerProxy()->GetSoundStrDefCsvIdMap().find(es.id());
			if (soundname != GetDefManagerProxy()->GetSoundStrDefCsvIdMap().end())
			{
				effectmgr->playSound(MPVEC2WCoord(es.pos()), soundname->second.c_str(),
					es.volume() / 1000.0, es.pitch() / 1000.0, es.flags() & ~PLAYSND_SYNC, es.segment() - 1, es.fixpitch());
			}
		}
	}
	else if (playEffectHC.effecttype() == PB_EFFECT_ACTORBODY)
	{
		const PB_EffectActorBody& eb = playEffectHC.actorbody();

		ClientActor* actor = actormgr->findActorByWID(eb.objid());
		if (actor)
		{
			if (eb.has_status())
			{
				auto effectComponent = actor->getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect((ACTORBODY_EFFECT)eb.bodyeffect());
				}
			}
			else
			{
				auto effectComponent = actor->getEffectComponent();
				if (effectComponent)
				{
					effectComponent->stopBodyEffect((ACTORBODY_EFFECT)eb.bodyeffect());
				}
			}
			if (eb.bodyeffect() == BODYFX_PORTAL)
			{
				auto portalComponent = actor->sureActorInPortal();
				if (portalComponent)
				{
					portalComponent->setInPortalStatus(eb.has_status());
				}
			}
		}
	}
	else if (playEffectHC.effecttype() == PB_EFFECT_STRINGACTORBODY)
	{
		const PB_EffectStringActorBody& eb = playEffectHC.stringactorbody();
		float effectScale = playEffectHC.effectscale() / 1000.0f;
		float loopPlayTime = eb.loopplaytime();

		ClientActor* actor = actormgr->findActorByWID(eb.objid());
		if (actor)
		{
			auto effectComponent = actor->getEffectComponent();
			if (effectComponent)
			{
				if (eb.status() == 0)
				{
					effectComponent->playBodyEffectForTrigger((char*)eb.effectname().c_str(), effectScale, true, loopPlayTime);
				}
				else if (eb.status() == 10 || eb.status() == 1000) {
					effectComponent->playBodyEffect((char*)eb.effectname().c_str(), true, loopPlayTime);
				}
				else
					effectComponent->stopBodyEffect((char*)eb.effectname().c_str());
			}
		}
	}
	else if (playEffectHC.effecttype() == PB_EFFECT_DESTROYBLOCK)
	{
		const PB_EffectDestroyBlock& ed = playEffectHC.destroyblock();

		if (effectmgr) effectmgr->playBlockDestroyEffect(ed.subtype(), MPVEC2WCoord(ed.pos()), (DirectionType)ed.face(), ed.age(), ed.id());
	}
	else if (playEffectHC.effecttype() == PB_EFFECT_CRACKBLOCK)
	{
		const PB_EffectCrackBlock& ed = playEffectHC.crackblock();

		if (effectmgr && playerCtrl->getObjId() != ed.actorid()) effectmgr->GetGameEffectMgr()->playBlockCrackEffect(MPVEC2WCoord(ed.blockpos()), ed.stage(), ed.actorid());
	}
	else if (playEffectHC.effecttype() == PB_EFFECT_VEHICLE)
	{
		const PB_EffectVehicle& ev = playEffectHC.effectvehicle();
		if (effectmgr) effectmgr->GetGameEffectMgr()->playParticleEffectForVehicle(ev.name().c_str(), 1, ev.actorid(), MPVEC2WCoord(ev.blockpos()), ev.age());
	}
	else if (playEffectHC.effecttype() == PB_EFFECT_PLAYMUSICGRID)
	{
		const PB_EffectPlayMusicGrid& epmg = playEffectHC.playmusicgrid();
		if (effectmgr) effectmgr->playMusicGrid(MPVEC2WCoord(epmg.blockpos()), epmg.name().c_str(), epmg.pitch(), (epmg.flags() & 1) > 0, epmg.segment() - 1);
	}
	else if (playEffectHC.effecttype() == PB_EFFECT_STOPMUSICGRID)
	{
		if (effectmgr) effectmgr->stopMusicGrid(MPVEC2WCoord(playEffectHC.stopmusicgrid().blockpos()));
	}
	else if (playEffectHC.effecttype() == PB_EFFECT_TIRGGERSOUND)
	{
		const PB_EffectTriggerSound& sound = playEffectHC.triggersound();
		ClientActor* actor = actormgr->findActorByWID(sound.objid());

		int playState = sound.playstate();
		if (actor != NULL) { //指定生物的音效操作
			if (playState == PLAYSTAT_PLAY)
			{
				auto soundComp = actor->getSoundComponent();
				if (soundComp)
				{
					if (sound.has_dist())
					{
						soundComp->skillPlaySound(sound.name().c_str(), sound.isloop(), sound.dist());
					}
					else
					{
						soundComp->playSoundByTrigger(sound.name().c_str(), sound.volume(), sound.pitch(), sound.isloop(), false, sound.position());
					}
					
				}
			}
			else if (playState == PLAYSTAT_STOP)
			{
				auto soundComp = actor->getSoundComponent();
				if (soundComp)
				{
					soundComp->stopSoundByTrigger(sound.name().c_str(), false);
				}
			}
		}
		else { //指定位置的音效操作
			if (effectmgr)
			{
				if (playState == PLAYSTAT_PLAY)
				{
					if (sound.has_dist())
					{
						effectmgr->skillPlayPosSound(MPVEC2WCoord(sound.pos()), sound.name().c_str(), sound.isloop(), sound.dist());
					}else
					{
						effectmgr->playPosSound(MPVEC2WCoord(sound.pos()), sound.name().c_str(), sound.volume(), sound.pitch(), sound.isloop(), false, sound.position());
					}
					
				}
				else if (playState == PLAYSTAT_STOP)
				{
					effectmgr->stopPosSound(MPVEC2WCoord(sound.pos()), sound.name().c_str(), false);
				}
				else if (playState == PLAYSTAT_PLAYEX)
				{
					if (playerCtrl->getUin() && playerCtrl->getUin() != playEffectHC.effectclass())
					{
						float effectScale = playEffectHC.effectscale();
						const PB_EffectParticle& particle = playEffectHC.particle();
						WCoord pos = MPVEC2WCoord(particle.pos());
						char path[256] = { 0 };
						sprintf(path, "%s", particle.name().c_str());
						int lifeTime = particle.has_age() ? particle.age() : 0;
						effectmgr->playParticleEffectForTrigger(path, pos, effectScale, lifeTime);

						WCoord soundPos = MPVEC2WCoord(sound.pos());
						int position = sound.position();
						effectmgr->playPosSoundEX(soundPos, sound.name().c_str(), sound.volume(), sound.pitch(), sound.isloop(), false, position);
					}
				}
			}
		}
	}
	else if (playEffectHC.effecttype() == PB_EFFECT_SOUND_NEW || playEffectHC.effecttype() == PB_EFFECT_SOUND_NEW_FOR_TRACK)
	{
		if (effectmgr)
		{
			const PB_EffectSoundNew& sound = playEffectHC.soundnew();
			LongSound ss;
			ss.volume = sound.volume();
			ss.pitch = sound.pitch();
			ss.duration = sound.duration();
			ss.startTime = sound.starttime();
			strcpy(ss.path, sound.name().c_str());
			strcpy(ss.extraStr, sound.extrastr().c_str());
			strcpy(ss.url, sound.url().c_str());
			ss.instrumentCode = sound.instrumentcode();
			ss.trackId = sound.trackid();
			ss.soundPos = sound.soundpos();
			ss.setPos(MPVEC2WCoord(sound.pos()));
			ss.objid = sound.objid();
			ss.type = sound.soundtype();
			ss.isLoop = sound.isloop();
			ss.effecttype = playEffectHC.effecttype();
			effectmgr->playLongSoundClient(ss, playEffectHC.effecttype());
		}
	}
	else if (playEffectHC.effecttype() == PB_EFFECT_SOUND_NEW_STOP)
	{
		if (effectmgr)
		{
			const PB_EffectSoundNew& sound = playEffectHC.soundnew();
			if (sound.objid() > 0)
			{
				effectmgr->doStopLongSound3D(sound.objid(), sound.name());
			}
			else if (sound.objid() == -1)
			{
				effectmgr->doStopLongSound2D(sound.name());
			}
			else
			{
				effectmgr->doStopLongSound3D(MPVEC2WCoord(sound.pos()), sound.name());
			}
		}
	}
	else if (playEffectHC.effecttype() == PB_EFFECT_SOUND_NOTE)
	{
		if (effectmgr)
		{
			const PB_EffectSoundNew& sound = playEffectHC.soundnew();
			effectmgr->doPlayMidiMusicNote(sound.objid(), sound.notecode(), sound.instrumentcode(), sound.volume(), sound.tpqcount());
		}
	}
	else if (playEffectHC.effecttype() == PB_EFFECT_SOUND_NOTE_STOP)
	{
		if (effectmgr)
		{
			const PB_EffectSoundNew& sound = playEffectHC.soundnew();
			effectmgr->doStopMidiMusicNote(sound.objid(), sound.notecode(), sound.instrumentcode());
		}
	}
}


void MpGameSurviveNetHandler::handlePlayEffect2Client_V2(const PB_PACKDATA_CLIENT& pkg)
{
	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL || playerCtrl->getWorld() == NULL) return;

	PB_PlayEffectHC_V2 playEffectHC_v2;
	playEffectHC_v2.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (playerCtrl == NULL) return;
	if (playerCtrl->getWorld() == NULL) return;
	EffectManager* effectmgr = playerCtrl->getWorld()->getEffectMgr();
	ClientActorMgr* actormgr = playerCtrl->getWorld()->getActorMgr()->ToCastMgr();

	if (playEffectHC_v2.effecttype() == PB_EFFECT_PARTICLE)
	{
		const PB_EffectParticle& pt = playEffectHC_v2.particle();
		if (effectmgr)
		{
			if (playEffectHC_v2.has_effectscale())
			{
				float effectScale = playEffectHC_v2.effectscale();

				//触发器
				int lifeTime = pt.has_age() ? pt.age() : 0;
				if (playEffectHC_v2.has_useplayerviewrange() && playEffectHC_v2.useplayerviewrange())
					effectmgr->playParticleEffectForTriggerUsePlayerViewRange(pt.name().c_str(), MPVEC2WCoord(pt.pos()), effectScale / 1000.0f, lifeTime);
				else
					effectmgr->playParticleEffectForTrigger(pt.name().c_str(), MPVEC2WCoord(pt.pos()), effectScale / 1000.0f, lifeTime);
			}
			else
			{
				
				if (pt.has_ziprespath())
				{
					char zipPath[256] = { 0 };
					sprintf(zipPath, "%s", pt.ziprespath().c_str());
					effectmgr->playParticleEffectAsync(pt.name().c_str(), MPVEC2WCoord(pt.pos()), pt.age(), pt.yaw(), pt.pitch(), false, 0, 0, Rainbow::ColorQuad(pt.color()), zipPath, pt.ispersistent());
				}
				else
				{
					effectmgr->playParticleEffectAsync(pt.name().c_str(), MPVEC2WCoord(pt.pos()), pt.age(), pt.yaw(), pt.pitch(), false, 0, 0, Rainbow::ColorQuad(pt.color()), nullptr, pt.ispersistent());
				}
			}
		}
	}
	else if (playEffectHC_v2.effecttype() == PB_EFFECT_PARTICLEID)
	{
		if (effectmgr)
		{
			const PB_EffectParticleID_V2& pt = playEffectHC_v2.particleid();
			auto effectpath = GetDefManagerProxy()->GetParticlesStrDefCsvIdMap().find(pt.id());
			if (effectpath != GetDefManagerProxy()->GetParticlesStrDefCsvIdMap().end())
			{
				if (pt.pos_size() != 3)
					return;
				if (playEffectHC_v2.has_effectscale())
				{
					//录像中存在bug，effectscale会传入默认的1.0f
					//但通过网络传的effectscale，如果是默认1.0f，则不会传；否则*1000
					float pbscale = playEffectHC_v2.effectscale();
					if (pbscale == 1.0f) {
						pbscale = 1000.0f;
					}
					//触发器
					int lifeTime = pt.has_age() ? pt.age() : 0;
					if (playEffectHC_v2.has_useplayerviewrange() && playEffectHC_v2.useplayerviewrange())
						effectmgr->playParticleEffectForTriggerUsePlayerViewRange(effectpath->second.c_str(),
							WCoord(pt.pos(0), pt.pos(1), pt.pos(2)), pbscale / 1000.0f, lifeTime);
					else
						effectmgr->playParticleEffectForTrigger(effectpath->second.c_str(),
							WCoord(pt.pos(0), pt.pos(1), pt.pos(2)), pbscale / 1000.0f, lifeTime);
				}
				else
				{
					uint32_t yaw_pitch = pt.yaw_pitch();
					float yaw = AngleChar2Float((yaw_pitch & 0xff00) >> 8);
					float pitch = AngleChar2Float(yaw_pitch & 0xff);
					int age = pt.age();
					if (pt.has_ziprespath())
					{
						char zipPath[256] = { 0 };
						sprintf(zipPath, "%s", pt.ziprespath().c_str());
						effectmgr->playParticleEffectAsync(effectpath->second.c_str(), WCoord(pt.pos(0), pt.pos(1), pt.pos(2)), age,
							yaw, pitch, false, 0, 0, Rainbow::ColorQuad(pt.color()), zipPath);
					}
					else
					{
						EffectParticle * tr = effectmgr->playParticleEffectAsync(effectpath->second.c_str(), WCoord(pt.pos(0), pt.pos(1), pt.pos(2)), age,
							yaw, pitch, false, 0, 0, Rainbow::ColorQuad(pt.color()));

						if (tr && age > 0 && playEffectHC_v2.has_effectclass() && playEffectHC_v2.effectclass() == EFFECT_CLASS_TRIGGER)
						{
							tr->setLoop(true);
						}
					}
				}
			}
		}
	}
	else if (playEffectHC_v2.effecttype() == PB_EFFECT_STOPPARTICLE)
	{
		const PB_EffectParticle& pt = playEffectHC_v2.particle();
		if (effectmgr)
		{
			EffectParticle* par = effectmgr->getParticleOnPos(MPVEC2WCoord(pt.pos()), pt.name().c_str());
			if (par != NULL)
			{
				effectmgr->stopParticleEffectByID(par->_ID);
				//effectmgr->stopParticleEffect(pt.name().c_str(), MPVEC2WCoord(pt.pos()));
			}
		}
	}
	else if (playEffectHC_v2.effecttype() == PB_EFFECT_PICKITEM)
	{
		if (actormgr == NULL) return;
		const PB_EffectPickItem& pi = playEffectHC_v2.pickitem();
		ClientPlayer* player = actormgr->findPlayerByUin(int(pi.pickerobj()));
		ClientActor* item = actormgr->findActorByWID(pi.itemobj());

		if (player && item)
		{
			if (effectmgr) effectmgr->GetGameEffectMgr()->playPickItemEffect(player, item, pi.yoffset());
		}
	}
	else if (playEffectHC_v2.effecttype() == PB_EFFECT_SOUND)
	{
		const PB_EffectSound_V2& es = playEffectHC_v2.sound();
		if (es.effnd_size() != 7)
			return;
		if (effectmgr)
			effectmgr->playSound(WCoord(es.effnd(4), es.effnd(5), es.effnd(6)), es.name().c_str(), es.effnd(0) / 1000.0, es.effnd(1) / 1000.0, es.effnd(2) & ~PLAYSND_SYNC, es.effnd(3) - 1);
	}
	else if (playEffectHC_v2.effecttype() == PB_EFFECT_SOUNDID)
	{
		if (effectmgr)
		{
			const PB_EffectSoundID_V2& es = playEffectHC_v2.soundid();
			if (es.effd_size() != 8)
				return;
			auto soundname = GetDefManagerProxy()->GetSoundStrDefCsvIdMap().find(es.effd(0));
			if (soundname != GetDefManagerProxy()->GetSoundStrDefCsvIdMap().end())
			{
				effectmgr->playSound(WCoord(es.effd(5), es.effd(6), es.effd(7)), soundname->second.c_str(),
					es.effd(1) / 1000.0, es.effd(2) / 1000.0, es.effd(3) & ~PLAYSND_SYNC, es.effd(4) - 1);
			}
		}
	}
	else if (playEffectHC_v2.effecttype() == PB_EFFECT_ACTORBODY)
	{
		const PB_EffectActorBody& eb = playEffectHC_v2.actorbody();

		ClientActor* actor = actormgr->findActorByWID(eb.objid());
		if (actor)
		{
			if (eb.has_status())
			{
				auto effectComponent = actor->getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect((ACTORBODY_EFFECT)eb.bodyeffect());
				}
			}
			else
			{
				auto effectComponent = actor->getEffectComponent();
				if (effectComponent)
				{
					effectComponent->stopBodyEffect((ACTORBODY_EFFECT)eb.bodyeffect());
				}
			}
			if (eb.bodyeffect() == BODYFX_PORTAL)
			{
				auto portalComponent = actor->sureActorInPortal();
				if (portalComponent)
				{
					portalComponent->setInPortalStatus(eb.has_status());
				}
			}
		}
	}
	else if (playEffectHC_v2.effecttype() == PB_EFFECT_STRINGACTORBODY)
	{
		const PB_EffectStringActorBody& eb = playEffectHC_v2.stringactorbody();
		float effectScale = playEffectHC_v2.effectscale() / 1000.0f;

		ClientActor* actor = actormgr->findActorByWID(eb.objid());
		if (actor)
		{
			auto effectComponent = actor->getEffectComponent();
			if (effectComponent)
			{
				auto effectComponent = actor->getEffectComponent();
				if (effectComponent)
				{
					if (eb.status() == 0)
					{
						effectComponent->playBodyEffectForTrigger((char*)eb.effectname().c_str(), effectScale);
					}
					else if (eb.status() == 10 || eb.status() == 1000) {
						effectComponent->playBodyEffect((char*)eb.effectname().c_str());
					}
					else
						effectComponent->stopBodyEffect((char*)eb.effectname().c_str());
				}
			}

		}
	}
	else if (playEffectHC_v2.effecttype() == PB_EFFECT_DESTROYBLOCK)
	{
		const PB_EffectDestroyBlock& ed = playEffectHC_v2.destroyblock();

		if (effectmgr) effectmgr->playBlockDestroyEffect(ed.subtype(), MPVEC2WCoord(ed.pos()), (DirectionType)ed.face(), ed.age(), ed.id());
	}
	else if (playEffectHC_v2.effecttype() == PB_EFFECT_CRACKBLOCK)
	{
		const PB_EffectCrackBlock& ed = playEffectHC_v2.crackblock();

		if (effectmgr && playerCtrl->getObjId() != ed.actorid()) effectmgr->GetGameEffectMgr()->playBlockCrackEffect(MPVEC2WCoord(ed.blockpos()), ed.stage(), ed.actorid());
	}
	else if (playEffectHC_v2.effecttype() == PB_EFFECT_VEHICLE)
	{
		const PB_EffectVehicle& ev = playEffectHC_v2.effectvehicle();
		if (effectmgr) effectmgr->GetGameEffectMgr()->playParticleEffectForVehicle(ev.name().c_str(), 1, ev.actorid(), MPVEC2WCoord(ev.blockpos()), ev.age());
	}
	else if (playEffectHC_v2.effecttype() == PB_EFFECT_PLAYMUSICGRID)
	{
		const PB_EffectPlayMusicGrid& epmg = playEffectHC_v2.playmusicgrid();
		if (effectmgr) effectmgr->playMusicGrid(MPVEC2WCoord(epmg.blockpos()), epmg.name().c_str(), epmg.pitch(), (epmg.flags() & 1) > 0, epmg.segment() - 1);
	}
	else if (playEffectHC_v2.effecttype() == PB_EFFECT_STOPMUSICGRID)
	{
		if (effectmgr) effectmgr->stopMusicGrid(MPVEC2WCoord(playEffectHC_v2.stopmusicgrid().blockpos()));
	}
	else if (playEffectHC_v2.effecttype() == PB_EFFECT_TIRGGERSOUND)
	{
		const PB_EffectTriggerSound& sound = playEffectHC_v2.triggersound();
		ClientActor* actor = actormgr->findActorByWID(sound.objid());

		int playState = sound.playstate();
		if (actor != NULL) { //指定生物的音效操作
			if (playState == PLAYSTAT_PLAY)
			{
				auto soundComp = actor->getSoundComponent();
				if (soundComp)
				{
					if (sound.has_dist())
					{
						soundComp->skillPlaySound(sound.name().c_str(), sound.isloop(), sound.dist());
					}
					else
					{
						soundComp->playSoundByTrigger(sound.name().c_str(), sound.volume(), sound.pitch(), sound.isloop(), false, sound.position());
					}
				}
			}
			else if (playState == PLAYSTAT_STOP)
			{
				auto soundComp = actor->getSoundComponent();
				if (soundComp)
				{
					soundComp->stopSoundByTrigger(sound.name().c_str(), false);
				}
			}
		}
		else { //指定位置的音效操作
			if (effectmgr)
			{
				if (playState == PLAYSTAT_PLAY)
				{
					if (sound.has_dist())
					{
						effectmgr->skillPlayPosSound(MPVEC2WCoord(sound.pos()), sound.name().c_str(), sound.isloop(), sound.dist());
					}
					else
					{
						effectmgr->playPosSound(MPVEC2WCoord(sound.pos()), sound.name().c_str(), sound.volume(), sound.pitch(), sound.isloop(), false, sound.position());
					}
				}
				else if (playState == PLAYSTAT_STOP)
				{
					effectmgr->stopPosSound(MPVEC2WCoord(sound.pos()), sound.name().c_str(), false);
				}
				else if (playState == PLAYSTAT_PLAYEX)
				{
					if (playerCtrl->getUin() && playerCtrl->getUin() != playEffectHC_v2.effectclass())
					{
						float effectScale = playEffectHC_v2.effectscale();
						const PB_EffectParticle& particle = playEffectHC_v2.particle();
						WCoord pos = MPVEC2WCoord(particle.pos());
						char path[256] = { 0 };
						sprintf(path, "%s", particle.name().c_str());
						int lifeTime = particle.age();
						effectmgr->playParticleEffectForTrigger(path, pos, effectScale, lifeTime);

						WCoord soundPos = MPVEC2WCoord(sound.pos());
						int position = sound.position();
						effectmgr->playPosSoundEX(soundPos, sound.name().c_str(), sound.volume(), sound.pitch(), sound.isloop(), false, position);
					}
				}
			}
		}
	}
	else if (playEffectHC_v2.effecttype() == PB_EFFECT_SOUND_NEW || playEffectHC_v2.effecttype() == PB_EFFECT_SOUND_NEW_FOR_TRACK)
	{
		if (effectmgr)
		{
			const PB_EffectSoundNew& sound = playEffectHC_v2.soundnew();
			LongSound ss;
			ss.volume = sound.volume();
			ss.pitch = sound.pitch();
			ss.duration = sound.duration();
			ss.startTime = sound.starttime();
			strcpy(ss.path, sound.name().c_str());
			strcpy(ss.extraStr, sound.extrastr().c_str());
			strcpy(ss.url, sound.url().c_str());
			ss.instrumentCode = sound.instrumentcode();
			ss.trackId = sound.trackid();
			ss.soundPos = sound.soundpos();
			ss.setPos(MPVEC2WCoord(sound.pos()));
			ss.objid = sound.objid();
			ss.type = sound.soundtype();
			ss.isLoop = sound.isloop();
			ss.effecttype = playEffectHC_v2.effecttype();
			effectmgr->playLongSoundClient(ss, playEffectHC_v2.effecttype());
		}
	}
	else if (playEffectHC_v2.effecttype() == PB_EFFECT_SOUND_NEW_STOP)
	{
		if (effectmgr)
		{
			const PB_EffectSoundNew& sound = playEffectHC_v2.soundnew();
			if (sound.objid() > 0)
			{
				effectmgr->doStopLongSound3D(sound.objid(), sound.name());
			}
			else if (sound.objid() == -1)
			{
				effectmgr->doStopLongSound2D(sound.name());
			}
			else
			{
				effectmgr->doStopLongSound3D(MPVEC2WCoord(sound.pos()), sound.name());
			}
		}
	}
	else if (playEffectHC_v2.effecttype() == PB_EFFECT_SOUND_NOTE)
	{
		if (effectmgr)
		{
			const PB_EffectSoundNew& sound = playEffectHC_v2.soundnew();
			effectmgr->doPlayMidiMusicNote(sound.objid(), sound.notecode(), sound.instrumentcode(), sound.volume(), sound.tpqcount());
		}
	}
	else if (playEffectHC_v2.effecttype() == PB_EFFECT_SOUND_NOTE_STOP)
	{
		if (effectmgr)
		{
			const PB_EffectSoundNew& sound = playEffectHC_v2.soundnew();
			effectmgr->doStopMidiMusicNote(sound.objid(), sound.notecode(), sound.instrumentcode());
		}
	}
}


//客机处理设置特效大小
void MpGameSurviveNetHandler::handleEffectScale2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL || playerCtrl->getWorld() == NULL)
		return;

	PB_EffectScaleHC effectscaleHC;
	effectscaleHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (playerCtrl == NULL)
		return;
	if (playerCtrl->getWorld() == NULL)
		return;

	EffectManager* effectmgr = playerCtrl->getWorld()->getEffectMgr();
	ClientActorMgr* actormgr = playerCtrl->getWorld()->getActorMgr()->ToCastMgr();

	const char* effectname = effectscaleHC.effectname().c_str();
	float effectscale = effectscaleHC.effectscale();
	long long objid = effectscaleHC.objid();

	if (effectscaleHC.effecttype() == PB_EFFECT_PARTICLE)
	{
		//位置上的特效
		if (effectmgr){
			WCoord wcoord = MPVEC2WCoord(effectscaleHC.pos());
			effectmgr->setParticleEffectScale(effectname, wcoord, effectscale);
		}
	}
	else if (effectscaleHC.effecttype() == PB_EFFECT_STRINGACTORBODY)
	{
		//玩家、生物、投掷物
		if (actormgr)
		{
			ClientActor* actor = actormgr->findActorByWID(objid);
			if (actor)
			{
				auto effectComponent = actor->getEffectComponent();
				if (effectComponent)
				{
					effectComponent->setBodyEffectScale(effectname, effectscale);
				}
			}
		}
	}
}

void MpGameSurviveNetHandler::handleBlockInteract2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_BlockInteractHC blockInteractHC;
	blockInteractHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = dynamic_cast<ClientPlayer *>(objId2ActorOnClient(blockInteractHC.objid()));
	if (player == NULL) return;

	player->interactBlock(MPVEC2WCoord(blockInteractHC.blockpos()), (DirectionType)blockInteractHC.face(), Rainbow::Vector3f(0, 0, 0));
}

void MpGameSurviveNetHandler::handleBlockPunch2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_BlockPunchHC blockPunchHC;
	blockPunchHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = dynamic_cast<ClientPlayer *>(objId2ActorOnClient(blockPunchHC.objid()));
	if (player == NULL) return;
	long long vehID = blockPunchHC.vehicleobjid();

	if (vehID != 0)
	{
		auto pState = player->getActionStatePtr("Dig");
		auto actor = objId2ActorOnClient(vehID);
		if (actor && pState)
		{
			auto vehActor = dynamic_cast<ActorVehicleAssemble*>(actor);
			auto pDigState = dynamic_cast<DigState*>(pState);
			if (vehActor)
			{
				if (pDigState)
				{
					pDigState->digBlockWithVehicle(MPVEC2WCoord(blockPunchHC.blockpos()), (DirectionType)blockPunchHC.face(), blockPunchHC.status(), vehActor, (DIG_METHOD_T)blockPunchHC.digmethod());
				}
				else {
					auto pRangDigState = dynamic_cast<RangeDigState*>(pState);
					if (pRangDigState)
					{
						pRangDigState->digBlockWithVehicle(MPVEC2WCoord(blockPunchHC.blockpos()), (DirectionType)blockPunchHC.face(), blockPunchHC.status(), vehActor, (DIG_METHOD_T)blockPunchHC.digmethod());
					}
				}
				
			}
			else {
				assert(false && "对应的actor不是载具");
			}
		}
		else {
			assert(false && "找不到响应的actor");
		}
	}
	else {
		auto pState = player->getActionStatePtr("Dig");
		if (nullptr != pState)
		{
			auto pDigState = dynamic_cast<DigState*>(pState);
			if (nullptr != pDigState)
			{
				pDigState->digBlock(MPVEC2WCoord(blockPunchHC.blockpos()), (DirectionType)blockPunchHC.face(), blockPunchHC.status(), (DIG_METHOD_T)blockPunchHC.digmethod());
			}
			else
			{
				auto pRangDigState = dynamic_cast<RangeDigState*>(pState);
				if (nullptr != pRangDigState)
				{
					pRangDigState->digBlock(MPVEC2WCoord(blockPunchHC.blockpos()), (DirectionType)blockPunchHC.face(), blockPunchHC.status(), (DIG_METHOD_T)blockPunchHC.digmethod());
				}
			}
		}
	}

	//auto pState = player->getCurrentActionStatePtr();
	
	//player->digBlock(MPVEC2WCoord(blockPunchHC.blockpos()), (DirectionType)blockPunchHC.face(), blockPunchHC.status(), (DIG_METHOD_T)blockPunchHC.digmethod());
}

void MpGameSurviveNetHandler::handleBlockExploit2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_BlockExploitHC blockExploitHC;
	blockExploitHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = dynamic_cast<ClientPlayer *>(objId2ActorOnClient(blockExploitHC.objid()));
	if (player == NULL) return;

	auto pState = player->getCurrentActionStatePtr();
	auto pExploitState = dynamic_cast<ExploitState*>(pState);
	if (nullptr != pExploitState)
	{
		pExploitState->exploitBlock(MPVEC2WCoord(blockExploitHC.blockpos()), (DirectionType)blockExploitHC.face(), blockExploitHC.status(), blockExploitHC.picktype());
	}
	//player->exploitBlock(MPVEC2WCoord(blockExploitHC.blockpos()), (DirectionType)blockExploitHC.face(), blockExploitHC.status(), blockExploitHC.picktype());
}

void MpGameSurviveNetHandler::handleSetHook2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_SetHookHC setHookHC;
	setHookHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = dynamic_cast<ClientPlayer *>(objId2ActorOnClient(setHookHC.objid()));
	if (player == NULL) return;

	player->ClientPlayer::setHookObj(setHookHC.hookid());
}

void MpGameSurviveNetHandler::handleItemUse2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ItemUseHC itemUseHC;
	itemUseHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (!playerCtrl) {
		return;
	}
	if (itemUseHC.objid() != playerCtrl->getObjId()) {
		return;
	}
	ClientPlayer *player = dynamic_cast<ClientPlayer *>(objId2ActorOnClient(itemUseHC.objid()));
	if (player == NULL) return;

	bool onshift = itemUseHC.shift() == 1 ? true : false;
	player->useItem(itemUseHC.itemid(), itemUseHC.status(), onshift);
}

void MpGameSurviveNetHandler::handleItemSkillUse2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ItemSkillUseHC itemSkillUseHC;
	itemSkillUseHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = dynamic_cast<ClientPlayer *>(objId2ActorOnClient(itemSkillUseHC.objid()));
	if (player == NULL) return;

	std::vector<WCoord> blockpos;
	std::vector<WORLD_ID> obj; 
	WCoord centerPos;
	Rainbow::Vector3f currentEyePos, currentDir;
	player->useItemSkill(itemSkillUseHC.itemid(), itemSkillUseHC.status(), itemSkillUseHC.skillid(), currentEyePos, currentDir, blockpos, obj, centerPos);
}

void MpGameSurviveNetHandler::handleSpecialItemUse2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_SpecialItemUseHC specialItemUseHC;
	specialItemUseHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (specialItemUseHC.itemid() < 0)
	{
		PlayerControl *playerCtrl = m_root->getPlayerControl();
		if (playerCtrl)
		{
			MINIW::ScriptVM::game()->callFunction("ClientStarConvertSuccess", "");
		}
	}
	else
		MINIW::ScriptVM::game()->callFunction("SetAccountItemTips", "ii", specialItemUseHC.itemid(), specialItemUseHC.itemnum());
}

void MpGameSurviveNetHandler::handleLeaveRoomInfo2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_LeaveRoomInfoHC leaveRoomInfoHC;
	leaveRoomInfoHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

#ifndef IWORLD_SERVER_BUILD
	// 出房间埋点
	GameAnalytics::TrackEvent("player_leave_room", {
		{"uin", GameAnalytics::Value(GetClientInfoProxy()->getUin())},
		{"cause", GameAnalytics::Value(leaveRoomInfoHC.cause())},
		{"kicker_type", GameAnalytics::Value(leaveRoomInfoHC.kickertype())},
		{"room_host_type", GameAnalytics::Value(GetGameInfoProxy()->GetRoomHostType())}
	});
#endif

	if (ROOM_SERVER_RENT == GetGameInfoProxy()->GetRoomHostType())
	{
		MINIW::ScriptVM::game()->callFunction("RSConnectLost", "ii", leaveRoomInfoHC.cause(),leaveRoomInfoHC.kickertype());
	}
	else
	{
		//MINIW::ScriptVM::game()->callFunction("RSConnectLost", "ii", leaveRoomInfoHC.cause(), 0);
		MpGameSurvive* mp = dynamic_cast<MpGameSurvive*>(m_root);
		if (mp)
		{
			if (mp->getCurLoadingState() < CLOAD_COMPLETE)
			{
				mp->setCurLadingState(-1 * abs(leaveRoomInfoHC.cause()));
			}
			mp->terminateMPGame(leaveRoomInfoHC.cause());
		}
		else
			MINIW::ScriptVM::game()->callFunction("RSConnectLost", "ii", leaveRoomInfoHC.cause(), 0);
	}

	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl)
	{
		//离开房间通知
		MNSandbox::GlobalNotify::GetInstance().NotifyClientAoiLeaveRoom(playerCtrl->getUin());
	}
}

void MpGameSurviveNetHandler::handleReplyApplyPermits2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_JruisdicTionHC jruisdicTionHC;
	jruisdicTionHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	MINIW::ScriptVM::game()->callFunction("OnRespApplyPermits", "i", jruisdicTionHC.ret());
}

void MpGameSurviveNetHandler::handleGunDoReload2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl)
	{
		PB_GunDoReloadHC gunDoReloadHC;
		gunDoReloadHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

		if(playerCtrl == NULL) return;
		if (gunDoReloadHC.iscustomgun())
		{
			if (gunDoReloadHC.curshortcut() == playerCtrl->getCurShortcut())
			{
				CustomGunUseComponent* gl = playerCtrl->sureCustomGunComponent();
				if (gl) gl->doReload(gunDoReloadHC.total(), gunDoReloadHC.has_num() ? -gunDoReloadHC.num() : 0);
			}
		}
		else
		{
			GunUseComponent* gl = playerCtrl->getGunLogical();
			if (gl) gl->doReload(gunDoReloadHC.total());
		}
		
	}
}

void MpGameSurviveNetHandler::handleActorInteract2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActorInteractHC actorInteractHC;
	actorInteractHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = dynamic_cast<ClientPlayer *>(objId2ActorOnClient(actorInteractHC.objid()));
	if (player == NULL) return;
	ClientActor *target = objId2ActorOnClient(actorInteractHC.target());
	if (target == NULL) return;

	player->interactActor(target, actorInteractHC.itype(), actorInteractHC.iplot());
}

void MpGameSurviveNetHandler::handleRClickUpInteract2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_RClickUpInteractHC rClickUpInteractHC;
	rClickUpInteractHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer* player = dynamic_cast<ClientPlayer*>(objId2ActorOnClient(rClickUpInteractHC.objid()));
	if (player == NULL) return;
	ClientActor* target = objId2ActorOnClient(rClickUpInteractHC.target());
	if (target == NULL) return;

	target->rightClickUpInteract(player);
}

void MpGameSurviveNetHandler::handlePlayerWakeUp2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_RClickUpInteractHC rClickUpInteractHC;
	rClickUpInteractHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL) return;
	playerCtrl->SetOffline(false);
}

void MpGameSurviveNetHandler::handlePlayerMount2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerMountActorHC playerMountActorHC;
	playerMountActorHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(playerMountActorHC.playeruin());
	if (player)
	{
		ClientActor *actor = playerMountActorHC.actorid() == 0 ? NULL : objId2ActorOnClient(playerMountActorHC.actorid());
		bool isforce = playerMountActorHC.force() == 1 ? true : false;
		player->mountActor(actor, isforce, playerMountActorHC.rideposindex());
	}
}

void MpGameSurviveNetHandler::handleActorMount2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActorMountActorHC actorMountActorHC;
	actorMountActorHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientActor *actorowner = actorMountActorHC.actoridowner() == 0 ? NULL : objId2ActorOnClient(actorMountActorHC.actoridowner());
	if (actorowner)
	{
		ClientActor *actor = actorMountActorHC.actorid() == 0 ? NULL : objId2ActorOnClient(actorMountActorHC.actorid());
		auto RidComp = actorowner->sureRiddenComponent();
		if (RidComp)
		{
			RidComp->mountActor(actor);
		}
	}
}

void MpGameSurviveNetHandler::handleActorReverseClient(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActorReverseHC actorReverseHC;
	actorReverseHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientActor *actor = actorReverseHC.actorid() == 0 ? NULL : objId2ActorOnClient(actorReverseHC.actorid());
	if (actor)
	{
		if (actorReverseHC.reverse())
			actor->setReverse(true);
		else
			actor->setReverse(false);
	}
}

void MpGameSurviveNetHandler::handleActorBindClient(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActorBindHC actorBindHC;
	actorBindHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WORLD_ID bindid = actorBindHC.actoridbind();

	ClientActor *actor = actorBindHC.actorid() == 0 ? NULL : objId2ActorOnClient(actorBindHC.actorid());
	if (actor)
	{
		WCoord pos(actorBindHC.offetbind().x(), actorBindHC.offetbind().y(), actorBindHC.offetbind().z());
		auto bindAComponent = actor->getBindActorCom();
		if(bindAComponent)
		{
			bindAComponent->setBindInfo(bindid, pos, true);
		}
	}
}

void MpGameSurviveNetHandler::handlePlayerSleep2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerSleepHC playerSleepHC;
	playerSleepHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if(playerCtrl == NULL) return;	
	if (playerSleepHC.flags() == 0)
	{
		ClientPlayer* player = uin2Player(playerSleepHC.uin());
		if (player)
		{
			const WCoord& pos = MPVEC2WCoord(playerSleepHC.pos());
			if (player->getWorld() && IsSleepingbagBlock(playerCtrl->getWorld()->getBlockID(pos)))
			{
				player->sleep(pos);

				//客机睡袋需隐藏身体
				if (player->getBody())
					player->getBody()->show(false);
			}
			else
			{
				player->sleepInBed(MPVEC2WCoord(playerSleepHC.pos()),true);
			}
		}
	}
	else if (playerSleepHC.flags() == 1 || playerSleepHC.flags() == 2)
	{
		ClientPlayer* player = uin2Player(playerSleepHC.uin());
		if (player)
		{
			player->wakeUp(playerSleepHC.flags() == 2, false, false);
			//客机睡袋醒来需显示身体
			if (player->getBody())
			{
				player->getBody()->show(true);
			}
		}
	}
	else if (playerSleepHC.flags() == 3)
	{
		playerCtrl->sitInChair(MPVEC2WCoord(playerSleepHC.pos()));
	}
	else if (playerSleepHC.flags() == 4)
	{
		playerCtrl->standUpFromChair();
	}
	else if (playerSleepHC.flags() == 5)
	{
		ClientPlayer* player = uin2Player(playerSleepHC.uin());
		if (player)
		{
			auto sleepState = dynamic_cast<SleepState*>(player->getLocoCurActionStatePtr("Sleep"));
			if (sleepState)
			{
				sleepState->setRestInBed(false);
				sleepState->setSleeping(true);
			}
			/*player->setRestInBed(false);
			player->setSleeping(true);*/
		}
	}
	else if (playerSleepHC.flags() == 6)
	{
		playerCtrl->sitInChairEX(MPVEC2WCoord(playerSleepHC.pos()), MPVEC2WCoord(playerSleepHC.targetpos()));
	}
}

void MpGameSurviveNetHandler::handleMobBodyChange2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_MobBodyChangeHC mobBodyChangeHC;
	mobBodyChangeHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientMob *mob = dynamic_cast<ClientMob *>(objId2ActorOnClient(mobBodyChangeHC.objid()));
	if (mob == NULL) return;

	mob->setColor(mobBodyChangeHC.bodycolor());
	mob->setSheared(mobBodyChangeHC.sheared() != 0);
}

void MpGameSurviveNetHandler::handleOpenWindow2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_OpenWindowHC openWindowHC;
	openWindowHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl)
		playerCtrl->notifyOpenWindow2Self(openWindowHC.id(), openWindowHC.x(), openWindowHC.y(), openWindowHC.z());
}

void MpGameSurviveNetHandler::handleBackPackGridUpdate2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if(playerCtrl == NULL) return;
	auto backpack = playerCtrl->getBackPack();
	if (nullptr == backpack) return;

	PB_BackPackGridUpdateHC backPackGridUpdateHC;
	backPackGridUpdateHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int itemNum = backPackGridUpdateHC.iteminfo_size();
	for (int i = 0; i < itemNum; ++i)
	{
		const PB_ItemData &src = backPackGridUpdateHC.iteminfo(i);
		BackPackGrid *dest = backpack->index2Grid(src.index());
		restoreGridData(dest, src);
		backpack->afterChangeGrid(src.index());

		if (BackPack::isBackpackIndex(src.index()))
		{
			auto grid = backpack->index2Grid(src.index());
			if (grid)
				playerCtrl->updateTask(PAY_ITEM, grid->getItemID(), 0);
		}

	}

	GetGameEventQue().postBackpackChange(-1);
}

void MpGameSurviveNetHandler::handleBackPackGridEquipWeapon2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_BackPackEquipWeaponHC backPackEquipWeaponHC;
	backPackEquipWeaponHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if(playerCtrl == NULL) return;
	playerCtrl->onSetCurShortcut(backPackEquipWeaponHC.gridid());
}

void MpGameSurviveNetHandler::handleNeedContainerPassword2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_NeedContainerPasswordHC needContainerPasswordHC;
	needContainerPasswordHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	PB_Vector3* pos = needContainerPasswordHC.mutable_pos();
	ClientWorldContainer* container = NULL;
	MpGameSurvive*mp = dynamic_cast<MpGameSurvive*>(m_root);
	if (mp)
	{
		container = mp->getClientWorldContainer();
	}
	if (container)
	{
		container->m_BlockPos = MPVEC2WCoord(needContainerPasswordHC.pos());
		container->m_VehicleID = needContainerPasswordHC.has_vehicleobjid() ? needContainerPasswordHC.vehicleobjid() : 0;
	}

	GetGameEventQue().postNeedContainerPassword(pos->x(), pos->y(), pos->z(), needContainerPasswordHC.state());
}

void MpGameSurviveNetHandler::handleOpenHomeNpc2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_HomeNpcOpenHC homeNpcOpenHC;
	homeNpcOpenHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	GetGameEventQue().postOpenHomeNpc(homeNpcOpenHC.npctype(),homeNpcOpenHC.npcid());
}

void MpGameSurviveNetHandler::handleOpenContainer2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_OpenContainerHC openContainerHC;
	openContainerHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	MpGameSurvive* mp = dynamic_cast<MpGameSurvive*>(m_root);
	ClientWorldContainer* container = NULL;
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (mp)
	{
		container = mp->getClientWorldContainer();
		if (!container)
		{
			return;
		}
	}
	if (container->getBaseIndex() >= 0)
	{
		int baseindex = container->getBaseIndex();
		if (playerCtrl == NULL)
		{
			return;
		}
		playerCtrl->getBackPack()->detachContainer(container);
		GetGameEventQue().postCloseContainer(baseindex);
	}

	container->m_VehicleID = openContainerHC.has_vehicleobjid() ? openContainerHC.vehicleobjid() : 0;

	container->m_BlockPos = MPVEC2WCoord(openContainerHC.pos());
	container->m_NpcID = openContainerHC.npcid();
	container->reset(
		openContainerHC.baseindex(),
		openContainerHC.totalitemgrids(),
		&openContainerHC.iteminfo(),
		openContainerHC.iteminfo_size(),
		&openContainerHC.attribinfo(),
		openContainerHC.attribinfo_size(),
		openContainerHC.text().c_str());

	if (playerCtrl)
	{
		static_cast<MpPlayerControl *>(playerCtrl)->onOpenContainer(container);
	}
	

	// 如果是机械工作台 需要额外请求 item信息和当前是否锁定的状态
	if (openContainerHC.baseindex() == VEHICLE_START_INDEX)
	{
		PB_WorkshopItemInfoCH workshopItemInfoCH;
		PB_Vector3* pos = workshopItemInfoCH.mutable_containerpos();
		pos->set_x(container->m_BlockPos.x);
		pos->set_y(container->m_BlockPos.y);
		pos->set_z(container->m_BlockPos.z);
		GetGameNetManagerPtr()->sendToHost(PB_WORKSHOP_ITEMINFO_CH, workshopItemInfoCH);
	}
	else
	{
		if (g_pPlayerCtrl == NULL)
		{
			LOG_INFO("g_pPlayerCtrl is NULL");
			return;
		}
		long long objid = container->m_VehicleID;
		ClientActor *actor = objid > 0 ? g_pPlayerCtrl->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid) : NULL;

		int blockId = 1;
		ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
		if (vehicle&&vehicle->getVehicleWorld())
			blockId = vehicle->getVehicleWorld()->getBlockID(container->m_BlockPos);
		else if (g_pPlayerCtrl->getWorld())
			blockId = g_pPlayerCtrl->getWorld()->getBlockID(container->m_BlockPos);

		if (openContainerHC.text() == "OBJ_TYPE_CUBECHEST") {
			blockId = openContainerHC.npcid();
			container->m_NpcID = 0;
		}

		if (openContainerHC.text() == "OBJ_TYPE_PLAYERCORPSE") {
			blockId = openContainerHC.npcid();
			container->m_NpcID = 0;
		}

		GetGameEventQue().postOpenContainer(openContainerHC.baseindex(), blockId, container->m_BlockPos.x, container->m_BlockPos.y, container->m_BlockPos.z);
	}
}

void MpGameSurviveNetHandler::handleCloseContainer2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_CloseContainerHC closeContainerHC;
	closeContainerHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientWorldContainer* container = NULL;
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	MpGameSurvive*mp = dynamic_cast<MpGameSurvive*>(m_root);
	if (mp)
	{
		container = mp->getClientWorldContainer();
	}
	int baseIndex = closeContainerHC.baseindex();
	if (baseIndex == ACTORMODEL_START_INDEX)
		GetGameEventQue().postCloseEditActorModel();
	else if (container && container->getBaseIndex() == baseIndex)
	{
		if (playerCtrl)
		{
			static_cast<MpPlayerControl *>(playerCtrl)->onCloseContainer();
		}
		GetGameEventQue().postCloseContainer(baseIndex);
	}
}

void MpGameSurviveNetHandler::handleUpdateContainer2Client(const PB_PACKDATA_CLIENT &pkg)
{
	ClientWorldContainer* container = NULL;
	MpGameSurvive*mp = dynamic_cast<MpGameSurvive*>(m_root);
	if (mp)
	{
		container = mp->getClientWorldContainer();
	}
	PB_UpdateContainerHC updateContainerHC;
	updateContainerHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (container->m_BlockPos != MPVEC2WCoord(updateContainerHC.pos())
	|| container->getBaseIndex() != updateContainerHC.baseindex()) return;

	container->update(
		&updateContainerHC.iteminfo(),
		updateContainerHC.iteminfo_size(),
		&updateContainerHC.attribinfo(),
		updateContainerHC.attribinfo_size(),
		updateContainerHC.text().c_str());
}

void MpGameSurviveNetHandler::handleActorEquipItem2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActorEquipItemHC actorEquipItemHC;
	actorEquipItemHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	long long objId = actorEquipItemHC.objid();
	int slotType = actorEquipItemHC.slottype();
	if (slotType >= MAX_EQUIP_SLOTS) return;

	auto actor = objId2ActorOnClient(objId);
	auto living = actor != nullptr ? dynamic_cast<ActorLiving*>(actor) : nullptr;

	if (living != nullptr)
	{
		const PB_ItemData &itemData = actorEquipItemHC.iteminfo();
		auto attr = living->getLivingAttrib();

		auto playerAttr = dynamic_cast<PlayerAttrib *>(attr);
		if (playerAttr)
		{
			restoreGridData(playerAttr->getEquipGrid(EQUIP_SLOT_TYPE(slotType)), itemData);
			ClientPlayer* player = dynamic_cast<ClientPlayer*>(actor);
			if (player)
			{
				if (itemData.has_index()) //主机同步的其他玩家的快捷栏
				{
					int index = itemData.index() - player->getShortcutStartIndex();
					if (index >= 0 && index <= 7) //快捷栏栏位置
					{
						BackPack* backpack = player->getBackPack();
						if (backpack)
						{
							BackPackGrid* destGrid = backpack->index2Grid(itemData.index());
							if (destGrid)
							{
								if (itemData.itemid() > 0)
									restoreGridData(destGrid, itemData);
								else
									destGrid->clear();
							}
						}
						player->onSetCurShortcut(index);
					}
				}
				//应用装备，显示外观
				player->applyEquips(EQUIP_SLOT_TYPE(slotType));
				
				// 同步其他玩家的火把光照效果
				if (slotType == EQUIP_WEAPON)
				{
					// 获取世界对象和动态光照管理器
					World* world = player->getWorld();
					if (world && world->GetDynamicLightingManager())
					{
						// 获取玩家位置
						WCoord playerPos = CoordDivBlock(player->getPosition());
						int itemId = itemData.itemid();
						
						// 检查是否是火把类物品
						bool isLightEmittingItem = (itemId == BLOCK_TORCH || 
													itemId == BLOCK_SMALL_TORCH || 
													itemId == ITEM_SOCTORCH);
						
						if (isLightEmittingItem)
						{
							// 更新其他客户端的手持光源
							world->GetDynamicLightingManager()->updateOtherClientHeldLight(
								(int)objId, playerPos, itemId);
						}
						else
						{
							// 移除其他客户端的手持光源（如果之前有的话）
							world->GetDynamicLightingManager()->removeOtherClientHeldLight((int)objId);
						}
					}
				}
			}
		}
		else
		{
			attr->equip(EQUIP_SLOT_TYPE(slotType), itemData.itemid(), itemData.durable(), itemData.toughness(), itemData.maxdurable());
		}

		if(g_pPlayerCtrl && g_pPlayerCtrl->isInSpectatorMode() 
		&& g_pPlayerCtrl->getSpectatorType() == SPECTATOR_TYPE_FOLLW)
		{
			g_pPlayerCtrl->switchCurrentItem();
		}
	}
}

void MpGameSurviveNetHandler::handleEnchantSuccess2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_EnchantItemSuccessHC enchantItemSuccessHC;
	enchantItemSuccessHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int sucIdx = enchantItemSuccessHC.gridindex();
	GetGameEventQue().postEnchantResult(sucIdx);
}


void MpGameSurviveNetHandler::handleRuneOperateSuccess2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_RuneOperateSuccessHC runeOpHc;
	runeOpHc.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	GetGameEventQue().postRuneResult(runeOpHc.optype(), runeOpHc.gridindex(), runeOpHc.result());
}

void MpGameSurviveNetHandler::handleUpdatePotContainer2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_UpdatePotContainerHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientPlayer *player = uin2Player(msg.uin());
	if (player != NULL && player->getWorld())
	{
		WorldPot * pot = dynamic_cast<WorldPot *> (player->getWorld()->getContainerMgr()->getContainer(msg.x(), msg.y(), msg.z()));
		if (!pot)
			return;
		pot->UpdateData(msg.ismaking(), msg.progress());
	}
}

void MpGameSurviveNetHandler::handleRepairSuccess2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_RepairItemSuccessHC repairItemSuccessHC;
	repairItemSuccessHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int sucIdx = repairItemSuccessHC.gridindex();
	GetGameEventQue().postRepairResult(sucIdx);
}

void MpGameSurviveNetHandler::handleHeartBeat2Client(const PB_PACKDATA_CLIENT &pkg)
{
	//m_RecvHeartBeatTick = Rainbow::Timer::getSystemTick();

	PB_HeartBeatCH heartBeatCH;
	heartBeatCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	// 记录服务器时间戳和当前客户端时间戳  codeby:liusijia 20211015
	m_RecvedServerTimeStamp = heartBeatCH.server_time();
	m_RecvBeatClientTimeStamp = Rainbow::Timer::getSystemTick();

	unsigned int lasttick = heartBeatCH.beatcode();
	if (lasttick > 0)
	{
		unsigned int curtick = Rainbow::Timer::getSystemTick();
		float t = (float(Rainbow::Timer::getSystemTick() - lasttick)) / 1000.f;
		m_nNetDelayTick = t * 20 < 0 ? 0 : t * 20;
	}
}

void MpGameSurviveNetHandler::handleSyncGridUserData2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ItemGridUserData itemGridUserData;
	itemGridUserData.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(itemGridUserData.uin());
	if (player != nullptr)
	{
		player->setUserDataStr(itemGridUserData.gridindex(), itemGridUserData.userdatastr());
	}
}

void MpGameSurviveNetHandler::handleSyncTriggerBlock2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_SyncTriggerBlock syncTriggerBlock;
	syncTriggerBlock.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(syncTriggerBlock.uin());
	if (player != nullptr)
	{
		player->setLastTriggerBlock(MPVEC2WCoord(syncTriggerBlock.blockpos()));
	}
}

#include "container_alientotem.h"
void MpGameSurviveNetHandler::handleSyncTotemPoint2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_TotemPointHC totem;
	totem.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WorldManager *worldMgr = m_root->getWorldMgr();
	if(worldMgr)
	{
		int op = totem.op();
		WCoord blockpos = MPVEC2WCoord(totem.point());
		if(op == 0) worldMgr->getWorldInfoManager()->addTotemPoint(blockpos, totem.mapid());
		else if(op == 1) worldMgr->getWorldInfoManager()->removeTotemPoint(blockpos, totem.mapid());
		else if(op == 2) //重新激活外星图腾
		{
			World *pworld = worldMgr->getWorld(totem.mapid());
			if(pworld)
			{
				WorldAlienTotemContainer *container = dynamic_cast<WorldAlienTotemContainer *>(pworld->getContainerMgr()->getContainer(blockpos));
				if(container) container->startActive(0);

				WorldContainerLua* relContainer = dynamic_cast<WorldContainerLua *>(pworld->getContainerMgr()->getContainer(blockpos));
				if (relContainer && relContainer->getSubTypeLua() == 1)
				{
					relContainer->Event().Emit("startActive", SandboxContext(relContainer).SetData_Number(0));
				}
			}
		}
	}
}

void MpGameSurviveNetHandler::handleInviteJoinRoomClient(const PB_PACKDATA_CLIENT &pkg)
{
	PB_InviteJoinRoomHC inviteJoinRoomHC;
	inviteJoinRoomHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	MINIW::ScriptVM::game()->callFunction("ServerBeInviteJoinRoom", "iss", inviteJoinRoomHC.uin(), inviteJoinRoomHC.roomstate().c_str(), inviteJoinRoomHC.passworld().c_str());
}


void MpGameSurviveNetHandler::handleYMChangeRoleClient(const PB_PACKDATA_CLIENT& pkg)
{
	PB_YMChangeRoleHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("PB_YM_CHANGEROLE_HC_2", SandboxContext(nullptr).
			SetData_UserObject<PB_YMChangeRoleHC>("msg", msg)
		);

}

void MpGameSurviveNetHandler::handleSpectatorMode2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_SetSpectatorModeHC setSpectatorModeHC;
	setSpectatorModeHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WorldManager *worldMgr = m_root->getWorldMgr();
	ClientPlayer *player = uin2Player(setSpectatorModeHC.uin());
	if (player && worldMgr)
	{
	   	int spactormodepre =  player->getSpectatorMode();
		player->ClientPlayer::setSpectatorMode((PLAYER_SPECTATOR_MODE)setSpectatorModeHC.spectatormode());

		std::vector<IClientPlayer *> players;
		worldMgr->getAllPlayers(players);
		for(int i= 0; i<(int)players.size(); i++)
		{
			auto player = static_cast<ClientPlayer*>(players[i]);
			if(player->isInSpectatorMode() && g_pPlayerCtrl != player)
			{
				if (player->isVisible())
				{
					if(player->getBody())
					{
						player->getBody()->show(true);
					}
				}
				else
				{
					if(player->getBody())
						player->getBody()->show(false);
				}
			}
			if(g_pPlayerCtrl != players[i])
			{
				if (player->isVisible())
				{
					if(player->getBody())
					{
						player->getBody()->show(true);
					}
				}
			}
		}

#ifndef DEDICATED_SERVER
		if(g_pPlayerCtrl)
		g_pPlayerCtrl->CheckSpectatorPlayerShow();

		if (player == g_pPlayerCtrl)
		{
			if(setSpectatorModeHC.spectatormode() == SPECTATOR_MODE_JUDGE || spactormodepre == SPECTATOR_MODE_JUDGE)
			{
				MINIW::ScriptVM::game()->callFunction("SpectatorModeChange", "");
			}
		}
		#endif
	}
}

void MpGameSurviveNetHandler::handleSpectatorType2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_SetSpectatorTypeHC setSpectatorTypeHC;
	setSpectatorTypeHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(setSpectatorTypeHC.uin());
	if (player)
	{
		player->ClientPlayer::setSpectatorType((PLAYER_SPECTATOR_TYPE)setSpectatorTypeHC.spectatortype());
		if(g_pPlayerCtrl == NULL) return;
		if (player == g_pPlayerCtrl)
			g_pPlayerCtrl->CheckSpectatorPlayerShow();
	}
}

void MpGameSurviveNetHandler::handleSetSpetatorPlayer2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_SetSpectatorPlayerHC setSpectatorPlayerHC;
	setSpectatorPlayerHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if(g_pPlayerCtrl == NULL) return;
	ClientPlayer *player = uin2Player(setSpectatorPlayerHC.tospectatoruin());
	if (player)
	{
		if (player == g_pPlayerCtrl)
			g_pPlayerCtrl->setSpectatorUin(setSpectatorPlayerHC.spectatoruin());
	}
}

void MpGameSurviveNetHandler::handleSetPlayerModelAni2Client(const PB_PACKDATA_CLIENT &pkg)
{
	 PB_SetPlayerModelAniHC setPlayerModelAniHC;
	setPlayerModelAniHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if(g_pPlayerCtrl && g_pPlayerCtrl->getUin() == setPlayerModelAniHC.spectatoruin()
	&& g_pPlayerCtrl->isInSpectatorMode() && g_pPlayerCtrl->getSpectatorType() == SPECTATOR_TYPE_FOLLW)
	{
		  switch(setPlayerModelAniHC.modelanimaltype())
		  {
			case PERFORMARROWATTACKSTART:
				 g_pPlayerCtrl->m_PlayerAnimation->performArrowAttackStart();
				 break;
			case PERFORMARROWATTACKREADY:
				 g_pPlayerCtrl->m_PlayerAnimation->performArrowAttackReady();
				 break;
			case PERFORMARROWATTACKSHOOT:
				 g_pPlayerCtrl->m_PlayerAnimation->performArrowAttackShoot();
				 break;
			case PERFORMCHARGEDIGSTART:
				 g_pPlayerCtrl->m_PlayerAnimation->performChargeDigStart();
				 break;
			case PERFORMCHARGEDIGREADY:
				 g_pPlayerCtrl->m_PlayerAnimation->performChargeDigReady();
				 break;
			case PERFORMCHARGEDIGSHOOT:
				 g_pPlayerCtrl->m_PlayerAnimation->performChargeDigShoot();
				 break;
			case PERFORMCHARGECANCEL:
			     g_pPlayerCtrl->m_PlayerAnimation->performChargeCancel((DIG_METHOD_T)setPlayerModelAniHC.modelanimalext());
				 break;
			case PERFORMACTIONIDLE:
			     g_pPlayerCtrl->m_PlayerAnimation->performActionIdle();
				 break;
			case PERFORMIDLE:
			     g_pPlayerCtrl->m_PlayerAnimation->performIdle();
				 break;
			case PERFORMFIREGUN:
			     g_pPlayerCtrl->m_PlayerAnimation->performFireGun();
				 break;
			case PERFORMPULLINGGUN:
			     g_pPlayerCtrl->m_PlayerAnimation->performPullingGun();
				 break;				
			case PERFORMRELOADGUN:
			     g_pPlayerCtrl->m_PlayerAnimation->performReloadGun();
				 break;
			case PERFORMDIG:
			     g_pPlayerCtrl->m_PlayerAnimation->performDig((DIG_METHOD_T)setPlayerModelAniHC.modelanimalext());
				 break;
			case PERFORMEAT:
			     g_pPlayerCtrl->m_PlayerAnimation->performEat();
				 break;
			case PERFORMUSIC:
				SandboxEventDispatcherManager::GetGlobalInstance().
					Emit("Player_CallPlay", SandboxContext(nullptr).SetData_String("method", "onlyplay"));
				 break;
			case PERFORMSHEILDSTART:
				g_pPlayerCtrl->m_PlayerAnimation->performDefenceStart();
				break;
			case PERFORMSHEILDLOOP:
				g_pPlayerCtrl->m_PlayerAnimation->performDefenceLoop();
				break;
			case PERFORMSHEILDCANCEL:
				g_pPlayerCtrl->m_PlayerAnimation->performDefenceCancel((int)setPlayerModelAniHC.modelanimalext());
				break;
			case PERFORMDRINK:
				g_pPlayerCtrl->m_PlayerAnimation->performDrink();
				break;
		  }
	}
}

void MpGameSurviveNetHandler::handleSetMyViewModeToSpectator2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_SendMyViewmodeToSpectatorHC sendMyViewmodeToSpectatorHC;
	sendMyViewmodeToSpectatorHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (g_pPlayerCtrl && g_pPlayerCtrl->getUin() == sendMyViewmodeToSpectatorHC.spectatoruin()
	&& g_pPlayerCtrl->isInSpectatorMode() && g_pPlayerCtrl->getSpectatorType() == SPECTATOR_TYPE_FOLLW)
	{
		g_pPlayerCtrl->m_ViewMode_ToSpectator =  sendMyViewmodeToSpectatorHC.myviewmode();
		g_pPlayerCtrl->setViewMode(g_pPlayerCtrl->m_ViewMode);
	} 
}

void MpGameSurviveNetHandler::handleSetBobblingToSpectator2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_SetBobbingToSpectatorHC setBobbingHC;
	setBobbingHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (g_pPlayerCtrl && g_pPlayerCtrl->getUin() == setBobbingHC.spectatoruin()
	&& g_pPlayerCtrl->isInSpectatorMode() && g_pPlayerCtrl->getSpectatorType() == SPECTATOR_TYPE_FOLLW)
	{
		g_pPlayerCtrl->getCamera()->setBobbing(setBobbingHC.bobbing());
	} 
}

void MpGameSurviveNetHandler::handleBallOperate2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_BallOperateHC ballOperateHC;
	ballOperateHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = dynamic_cast<ClientPlayer *>(uin2Player(ballOperateHC.uin()));
	if (player)
	{
		if (ballOperateHC.type() == PLAYEROP_CATCH_BALL)
		{
			auto ball = ballOperateHC.actorid() == 0 ? NULL : objId2ActorOnClient(ballOperateHC.actorid());
			FootballStateAction::doCatchBall(player, ball);
			//player->doCatchBall(ball);
		}
		else if (ballOperateHC.type() == PLAYEROP_SHOOT || ballOperateHC.type() == PLAYEROP_PASS_BALL)
		{
			auto ball = ballOperateHC.actorid() == 0 ? NULL : objId2ActorOnClient(ballOperateHC.actorid());
			FootballStateAction::doKickBall(player, ballOperateHC.type(), (float)ballOperateHC.extenddata(), ball);
			//player->doKickBall(ballOperateHC.type(), (float)ballOperateHC.extenddata(), ball);
		}
		else if (ballOperateHC.type() == PLAYEROP_TACKLE)
		{
			FootballStateAction::doTackle(player);
			//player->doTackle();
		}
		else if (ballOperateHC.type() == PLAYEROP_TACKLE_END)
		{
			FootballStateAction::endTackle(player);
			//player->endTackle();
		}
		else if (ballOperateHC.type() == PLAYEROP_BALL_CHARGE_BEGIN)
		{
			FootballStateAction::beginChargeKickBall(player);
			//player->beginChargeKickBall();
		}
	}
}

void MpGameSurviveNetHandler::handleBasketBallOperator2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_BasketBallOperate basketballOperatorHC;
	basketballOperatorHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(uin2Player(basketballOperatorHC.uin()));
	if (player)
	{
		//篮球部分
		auto ball = basketballOperatorHC.actorid() == 0 ? NULL : objId2ActorOnClient(basketballOperatorHC.actorid());
		WCoord target_pos = MPVEC2WCoord(basketballOperatorHC.pos());
		if (basketballOperatorHC.type() == PLAYEROP_BASKETBALL_PASS)
		{
			BasketballStateAction::basketBallOPStart(player, basketballOperatorHC.type(), NULL);
			//player->basketBallOPStart(basketballOperatorHC.type(), NULL);
		}
		else if (basketballOperatorHC.type() == PLAYEROP_BASKETBALL_PASS_END)
		{
			BasketballStateAction::doKickBasketBall(player, basketballOperatorHC.type(), (BasketballFall)basketballOperatorHC.fallresult(), (float)basketballOperatorHC.extenddata(),ball, target_pos, basketballOperatorHC.yaw(), basketballOperatorHC.pitch(), basketballOperatorHC.selectedactorid());
			//player->doKickBasketBall(basketballOperatorHC.type(), (BasketballFall)basketballOperatorHC.fallresult(), (float)basketballOperatorHC.extenddata(),ball, target_pos, basketballOperatorHC.yaw(), basketballOperatorHC.pitch(), basketballOperatorHC.selectedactorid());
		}
		else if (basketballOperatorHC.type() == PLAYEROP_BASKETBALL_SHOOT)
		{
			BasketballStateAction::basketBallOPStart(player, basketballOperatorHC.type(), NULL);
			//player->basketBallOPStart(basketballOperatorHC.type(), NULL);
		}
		else if (basketballOperatorHC.type() == PLAYEROP_BASKETBALL_SHOOT_END)
		{
			BasketballStateAction::doKickBasketBall(player, basketballOperatorHC.type(), (BasketballFall)basketballOperatorHC.fallresult(), (float)basketballOperatorHC.extenddata(), ball, target_pos, basketballOperatorHC.yaw(), basketballOperatorHC.pitch(),basketballOperatorHC.selectedactorid());
			//player->doKickBasketBall(basketballOperatorHC.type(), (BasketballFall)basketballOperatorHC.fallresult(), (float)basketballOperatorHC.extenddata(), ball, target_pos, basketballOperatorHC.yaw(), basketballOperatorHC.pitch(),basketballOperatorHC.selectedactorid());
		}
		else if (basketballOperatorHC.type() == PLAYEROP_BASKETBALL_BLOCK_SHOT)
		{
			BasketballStateAction::basketBallOPStart(player, PLAYEROP_BASKETBALL_BLOCK_SHOT, NULL);
			//player->basketBallOPStart(PLAYEROP_BASKETBALL_BLOCK_SHOT, NULL);
		}
		else if (basketballOperatorHC.type() == PLAYEROP_BASKETBALL_BLOCK_SHOT_END)
		{
			BasketballStateAction::basketBallOPEnd(player,PLAYEROP_BASKETBALL_BLOCK_SHOT_END, NULL);
			//player->basketBallOPEnd(PLAYEROP_BASKETBALL_BLOCK_SHOT_END, NULL);
		}
		else if (basketballOperatorHC.type() == PLAYEROP_BASKETBALL_OBSTRUCT)
		{
			BasketballStateAction::basketBallOPStart(player, basketballOperatorHC.type(), NULL);
			//player->basketBallOPStart(basketballOperatorHC.type(), NULL);
		}
		else if (basketballOperatorHC.type() == PLAYEROP_BASKETBALL_OBSTRUCT_END)
		{
			BasketballStateAction::basketBallOPEnd(player, basketballOperatorHC.type(), NULL);
			//player->basketBallOPEnd(basketballOperatorHC.type(), NULL);
		}
		else if (basketballOperatorHC.type() == PLAYEROP_BASKETBALL_GRAB)
		{
			BasketballStateAction::basketBallOPStart(player, basketballOperatorHC.type(), NULL);
			//player->basketBallOPStart(basketballOperatorHC.type(), NULL);
		}
		else if (basketballOperatorHC.type() == PLAYEROP_BASKETBALL_GRAB_END)
		{
			BasketballStateAction::basketBallOPEnd(player, basketballOperatorHC.type(), NULL);
			//player->basketBallOPEnd(basketballOperatorHC.type(), NULL);
		}
		else if (basketballOperatorHC.type() == PLAYEROP_BASKETBALL_DRIBBLERUN)
		{
			auto ball = basketballOperatorHC.actorid() == 0 ? NULL : objId2ActorOnClient(basketballOperatorHC.actorid());
			BasketballStateAction::doRunDribbleRunBasketBall(player, ball);
			//player->doRunDribbleRunBasketBall(ball);
		}
		else if (basketballOperatorHC.type() == PLAYEROP_BASKETBALL_DRIBBLERUN_END)
		{
			BasketballStateAction::basketBallOPEnd(player,PLAYEROP_BASKETBALL_DRIBBLERUN_END, NULL);
			//player->basketBallOPEnd(PLAYEROP_BASKETBALL_DRIBBLERUN_END, NULL);
		}
		else if (basketballOperatorHC.type() == PLAYEROP_BASKETBALL_CHARGE_BEGIN)
		{
			BasketballStateAction::beginChargeThrowBall(player);
			//player->beginChargeThrowBall();
		}
	}
}

void MpGameSurviveNetHandler::handlePushSnowBallOperate2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_PushSnowBallOperateHC pushsnowballOperateHC;
	pushsnowballOperateHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer* player = dynamic_cast<ClientPlayer*>(uin2Player(pushsnowballOperateHC.uin()));
	if (player)
	{
		if (pushsnowballOperateHC.type() == PLAYEROP_PUSHSNOWBALL_SHOOT)
		{
			auto ball = pushsnowballOperateHC.actorid() == 0 ? NULL : objId2ActorOnClient(pushsnowballOperateHC.actorid());
			PushSnowBallStateAction::doKickBall(player, pushsnowballOperateHC.type(), (float)pushsnowballOperateHC.extenddata(), ball);
		}
		else if (pushsnowballOperateHC.type() == PLAYEROP_PUSHSNOWBALL_CHARGE_BEGIN)
		{
			PushSnowBallStateAction::beginChargeKickBall(player);
		}
		else if (pushsnowballOperateHC.type() == PLAYEROP_PUSHSNOWBALL_MAKEBALL && pushsnowballOperateHC.has_targetpos())
		{
			auto target = pushsnowballOperateHC.targetpos();
			WCoord pos(target.x(), target.y(), target.z());
			PushSnowBallStateAction::doMakeSnowBall(player, pos);
		}
		else if (pushsnowballOperateHC.type() == PLAYEROP_PUSHSNOWBALL_MAKEMAN)
		{
			auto catchball = pushsnowballOperateHC.actorid() == 0 ? NULL : player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(pushsnowballOperateHC.actorid());
			auto ball = pushsnowballOperateHC.extenddata() == 0 ? NULL : player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(pushsnowballOperateHC.extenddata());
			PushSnowBallStateAction::doMakeSnowMan(player, catchball, ball);
		}
	}
}

void MpGameSurviveNetHandler::handleGravityOperate2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_GravityOperateHC gravityOperateHC;
	gravityOperateHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = dynamic_cast<ClientPlayer *>(uin2Player(gravityOperateHC.uin()));
	if (player)
	{
		if (gravityOperateHC.type() == PLAYEROP_CATCH_GRAVITYACTOR)
		{
			auto actor = gravityOperateHC.actorid() == 0 ? NULL : objId2ActorOnClient(gravityOperateHC.actorid());
			player->doCatchGravityActor(actor);
		}
		else if (gravityOperateHC.type() == PLAYEROP_THROW_GRAVITYACTOR)
		{
			auto actor = gravityOperateHC.actorid() == 0 ? NULL : objId2ActorOnClient(gravityOperateHC.actorid());
			player->doThrowGravityActor(gravityOperateHC.type(), (float)gravityOperateHC.extenddata(), actor);
		}
		else if (gravityOperateHC.type() == PLAYEROP_GRAVITY_CHARGE_BEGIN)
		{
			player->beginChargeThrowGravityActor();
		}
	}
}

void MpGameSurviveNetHandler::handleResetRound2Client(const PB_PACKDATA_CLIENT &pkg)
{
	WorldManager *worldMgr = m_root->getWorldMgr();
	if (!worldMgr)
	{
		return;
	}
	GameMode *rulemgr = static_cast<GameMode*>(worldMgr->m_RuleMgr);
	if (rulemgr)
		rulemgr->clearDataByResetRound();
}

void MpGameSurviveNetHandler::handleRocketAttrChange2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_RocketAttribChangeHC rocketAttribChangeHC;
	rocketAttribChangeHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ActorRocket *rocket = dynamic_cast<ActorRocket *>(objId2ActorOnClient(rocketAttribChangeHC.objid()));
	if (rocket)
	{
		if(rocketAttribChangeHC.state() != -1)
			rocket->changeState((ROCKET_STATE)rocketAttribChangeHC.state(), false, true);
		if(rocketAttribChangeHC.fuel() != -1)
			rocket->changeFuel(rocketAttribChangeHC.fuel(), false, true);
	}
}

void MpGameSurviveNetHandler::handleAttractAttrChange2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_AttractAttribChangeHC attractAttribChangeHC;
	attractAttribChangeHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientActorAttract *attract = dynamic_cast<ClientActorAttract *>(objId2ActorOnClient(attractAttribChangeHC.objid()));
	if (attract)
	{
		if(attractAttribChangeHC.state() != -1)
			attract->setState(attractAttribChangeHC.state());
		if(attractAttribChangeHC.blockid() != -1)
			attract->setBlockID(Block::toMakeBlockIDWithEx(attractAttribChangeHC.blockid(), attractAttribChangeHC.blockexid()));
	}
}

void MpGameSurviveNetHandler::handleWorldTimes2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_WorldTimesHC worldTimesHC;
	worldTimesHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if(playerCtrl == NULL) return;
	for (int i = 0; i < MAX_MAP; i++)
	{
		playerCtrl->setWorldTime(i, worldTimesHC.times(i));
	}
}

void MpGameSurviveNetHandler::handleStatistic2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_StatisticHC statisticHC;
	statisticHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl)
	{
		std::string p5 = "";
		std::string p6 = "";
		std::string p7 = "";
		if (statisticHC.has_param5())
			p5 = statisticHC.param5();
		if (statisticHC.has_param6())
			p6 = statisticHC.param6();
		if (statisticHC.has_param7())
			p7 = statisticHC.param7();
		playerCtrl->statisticToWorld(playerCtrl->getUin(), statisticHC.eventid(), statisticHC.fristname().c_str(), statisticHC.worldtype(), statisticHC.param1().c_str(), statisticHC.param2().c_str(), statisticHC.param3().c_str(), statisticHC.param4().c_str(),
			p5.c_str(), p6.c_str(), p7.c_str());
	}
		
}

void MpGameSurviveNetHandler::handleHorsreFlyState2Client(const PB_PACKDATA_CLIENT &pkg)
{

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl)
	{
		auto playerRidComp = playerCtrl->getRiddenComponent();
		if (playerRidComp && playerRidComp->getRidingActor())
		{
			PB_HorseFlyStateHC horseFlyStateHC;
			horseFlyStateHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
			ActorHorse* horse = dynamic_cast<ActorHorse *>(playerRidComp->getRidingActor());
			if (horse)
			{
				horse->setEnergy(horseFlyStateHC.m_fenergy());
				horse->setTired(horseFlyStateHC.m_btired());
			}
		}
	}
	
}

void MpGameSurviveNetHandler::handleOpenDialogue2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_OpenDialogueHC openDialogueHC;
	openDialogueHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	WCoord pos = MPVEC2WCoord(openDialogueHC.openpos());
	ClientMob *mob = dynamic_cast<ClientMob *>(objId2ActorOnClient(openDialogueHC.objid()));

	if (mob == NULL && openDialogueHC.itemid() == 0) return;

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl)
	{
		if (mob)
		{
			auto plotCom = mob->GetComponent<PlotComponent>();
			if (plotCom)
			{
				plotCom->resetInteractData(&openDialogueHC.interactdata());
			}
			//mob->resetInteractData(&openDialogueHC.interactdata());
			//mob->EXEC_USEMODULE(resetInteractData, );
		}
			
		if (openDialogueHC.has_plottype() && openDialogueHC.plottype() > 0)
		{
			playerCtrl->setCurInteractPlotType(openDialogueHC.plottype());
		}
		playerCtrl->openPlotDialogue(mob, openDialogueHC.itemid(), pos);
	}
}

void MpGameSurviveNetHandler::handleCloseDialogue2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_CloseDialogueHC closeDialogueHC;
	closeDialogueHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl)
	{
		static_cast<MpPlayerControl *>(playerCtrl)->onCloseDialogue();
		GetGameEventQue().postClosePlotDialogue();
	}
}

void MpGameSurviveNetHandler::handleUpdateTask2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_UpdateTaskHC updateTaskHC;
	updateTaskHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl)
	{
		playerCtrl->updateTask((PLAYERTASK_TYPE)updateTaskHC.type(), updateTaskHC.id(), updateTaskHC.num());
	}
}

void MpGameSurviveNetHandler::handleSyncTaskEnterWorld2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_SyncTaskEnterWorldHC syncTaskEnterWorldHC;
	syncTaskEnterWorldHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl)
	{
		playerCtrl->syncTaskByEnterWorld(&syncTaskEnterWorldHC.taskinfodata());
	}
}

void MpGameSurviveNetHandler::handleCompleteTask2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_CompleteTaskHC completeTaskHC;
	completeTaskHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (g_pPlayerCtrl && g_pPlayerCtrl->m_pTaskMgr)
	{
		g_pPlayerCtrl->m_pTaskMgr->completeTask(completeTaskHC.taskid());
	}
}

void MpGameSurviveNetHandler::handleActorBodyTexture2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActorBodyTextureHC actorBodyTextureHC;
	actorBodyTextureHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientActor *actor = dynamic_cast<ClientActor *>(objId2ActorOnClient(actorBodyTextureHC.objid()));
	if (actor)
	{
		if (actorBodyTextureHC.has_texname()) {

			Rainbow::FixedString replaceTex = actorBodyTextureHC.texname().c_str();
			//MINIW::Texture *tex = static_cast<MINIW::Texture *>(MINIW::ResourceManager::getSingleton().blockLoad(replaceTex, MINIW::RLF_CONVERT_BIT16));
			Rainbow::SharePtr<Rainbow::Texture2D> tex = GetAssetManager().LoadAsset<Rainbow::Texture2D>(replaceTex);
			if (actor->getBody() && actor->getBody()->getEntity()) {
				const char* meshname = nullptr;
				if (actorBodyTextureHC.has_meshname()) {
					meshname = actorBodyTextureHC.meshname().c_str();
				}
				if (actor->getBody()->getEntity()->GetMainModel()) {
					actor->getBody()->getEntity()->GetMainModel()->SetTexture("g_DiffuseTex", tex, meshname);
				}
			}
		}
	}
}

void MpGameSurviveNetHandler::handlePlayerAddAvartar2Client(const PB_PACKDATA_CLIENT &pkg)
{
	if (GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
	{
		PB_PlayerAddAvartarHC addAvartarHC;
		addAvartarHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

		ClientPlayer *player = uin2Player(addAvartarHC.uin());
		if (player && player->getBody())
		{
			//player->getBody()->addAvatarPartModel(addAvartarHC.avatarmodel(), addAvartarHC.index());
			MINIW::ScriptVM::game()->callFunction("AddAvatarInfoByRecord", "iii", addAvartarHC.uin(), addAvartarHC.avatarmodel(), addAvartarHC.index());
		}
	}
}

void MpGameSurviveNetHandler::handlePlayerChangeModel2Client(const PB_PACKDATA_CLIENT &pkg)
{
   	if (GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
	{
		PB_PlayerChangeModelHC changeModelHC;
		changeModelHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

		ClientPlayer *player = uin2Player(changeModelHC.uin());
		if (player)
		{
			player->changePlayerModel(changeModelHC.playerindex(), 0, changeModelHC.customskins().c_str());
		}
	}
}

void MpGameSurviveNetHandler::handlePlayerTransformSkinModel2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerTransformSkinHC changeModelHC;
	changeModelHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(changeModelHC.uin());
	if (player)
	{
		player->onHandlePlayerTransformSkinModel2Client(changeModelHC);
	}
}

void MpGameSurviveNetHandler::handlePlayerAvartarColor2Client(const PB_PACKDATA_CLIENT &pkg)
{
	 if (GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
	{
		PB_PlayerAvartarColorHC addAvartarColorHC;
		addAvartarColorHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

		ClientPlayer *player = uin2Player(addAvartarColorHC.uin());
		if (player && player->getBody())
		{
			player->getBody()->alterAvatarPartColor(addAvartarColorHC.r(), addAvartarColorHC.g(), addAvartarColorHC.b(),
			(int)addAvartarColorHC.partid(), (int)addAvartarColorHC.modelid(), (int)addAvartarColorHC.block());
		}
	}
}

void MpGameSurviveNetHandler::handlePlayAct2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayActHC playActHC;
	playActHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(playActHC.uin());
	if (player && player->getBody())
	{
		//if (playActHC.actid() == player->getBody()->getActID() && playActHC.actid() > 0)
		//{
		//上面这个判断'playActHC.actid() == player->getBody()->getActID()'干什么的?
		auto actorBody = player->getBody();
		int actid = playActHC.actid();
		int actidTrigger = playActHC.actidtrigger();

		if(actid > 0 || actidTrigger > 0)
		{
			if (auto defTrigger = GetDefManagerProxy()->getTriggerActDef(actidTrigger))
			{
				// 触发器动作
				if (player == g_pPlayerCtrl)
				{
					player->playActForTrigger(actidTrigger);
				}
				else
				{
					actorBody->setActTrigger(actidTrigger);

					if (actorBody->getCurAnim(0) == SEQ_PLAY_ACT)
						actorBody->setCurAnim(-1, 0);
					actorBody->setCurAnim(SEQ_PLAY_ACT, 0);
					player->stopMotion(30000);
					player->playMotion(defTrigger->Effect.c_str(), 30000);
				}
			}
			else
			{
				// 普通动作
				if (player == g_pPlayerCtrl)
				{
					g_pPlayerCtrl->playAct(actid);
				}
				else
				{
					actorBody->setAct(actid);

					if (actorBody->getCurAnim(0) == SEQ_PLAY_ACT)
						actorBody->setCurAnim(-1, 0);
					actorBody->setCurAnim(SEQ_PLAY_ACT, 0);
					player->stopMotion(30000);
					if (auto def = GetDefManagerProxy()->getPlayActDef(actid))
					{
						player->playMotion(def->Effect.c_str(), 30000);
					}
				}
			}
		}
		//}
	}
}
//2021-09-14 codeby:chenwei 处理邀请方和被邀请方动作设置
void MpGameSurviveNetHandler::handlePlaySkinAct2Client(const PB_PACKDATA_CLIENT &pkg) //PB_PLAYER_SKIN_ACT_HC
{
	PB_PlaySkinActHC playSkinActHC;
	playSkinActHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int inviteUin = playSkinActHC.inviteuin();
	int acceptUin = playSkinActHC.acceptuin();

	//2021-09-17 codeby:chenwei 添加健壮性判断
	ClientPlayer *invitePlayer = uin2Player(inviteUin);
	ClientPlayer *acceptPlayer = uin2Player(acceptUin);

	if (invitePlayer == NULL || acceptPlayer == NULL)
		return;

	//20210927 codeby：chenwei 设置舞伴uin
	auto inviteBody = invitePlayer->getBody();
	auto acceptBody = acceptPlayer->getBody();

	if (inviteBody == NULL || acceptBody == NULL)
		return;

	//20211009 codeby:chenwei 停止老舞伴的动画
	int act = playSkinActHC.actid();

	//20211012 codeby:chenwei 非邀请方和被邀请方的老舞伴才停止动画,并优化接口参数
	auto stopSkinAct = [this, inviteUin, acceptUin, act](int uin) {
		if (uin > 0 && uin != inviteUin && uin != acceptUin)
		{
			ClientPlayer *player = uin2Player(uin);
			if (player && player->getBody())
			{
				ActorBody *body = player->getBody();
				const PlayActDef *def = GetDefManagerProxy()->getPlayActDef(body->getActID());
				if (def)
				{
					auto soundComp = player->getSoundComponent();
					if (soundComp)
					{
						soundComp->stopSoundByTrigger(def->Sound.c_str(), false);
					}
				}

				body->setAct(-1);
				body->setCurAnim(-1, 0);
				player->stopMotion(30000);
				player->setSkinPartnerUin(NULL);
			}
		}
	};
	stopSkinAct(invitePlayer->getSkinPartnerUin());
	stopSkinAct(acceptPlayer->getSkinPartnerUin());

	//20211008 codeby:chenwei 迁移接口位置，修改调用对象
	invitePlayer->setSkinPartnerUin(acceptUin);
	acceptPlayer->setSkinPartnerUin(inviteUin);

	auto setActCallFunc = [act](ActorBody* actorBody, bool isSideAct) {
		if (act > 0)
		{
			// 20210929 codeby:chenwei 代码格式优化
			actorBody->setSideAct(isSideAct);
			actorBody->setAct(act);

			//20210923 codeby:chenwei 装扮互动动作，更新序列号，并无需根据状态判断直接重置动作
			actorBody->setCurAnim(-1, 0);
			actorBody->setCurAnim(SEQ_PLAY_ACT, 0);
			actorBody->playSkinActMotion(act, 30000); //20210929 codeby:chenwei 播放装扮互动动作特效
			actorBody->setAnimSeq(actorBody->getAnimSeq() + 1);
		}
	};

	//20210927 codeby：chenwei 如果是自己则更改摄像机和摄影模式
	if (g_pPlayerCtrl && acceptUin == g_pPlayerCtrl->getUin())
	{
		g_pPlayerCtrl->m_pCamera->setRotate(invitePlayer->getLocoMotion()->m_RotateYaw - 180.0f, 0.0f);
		g_pPlayerCtrl->switchSkinActView();
	}

	//20210927 codeby：chenwei 移动到指定方位并播放动画 客机自己只需要调整自己的朝向就可以了
// 	if (acceptPlayer->getLocoMotion() && invitePlayer->getLocoMotion())
// 	{
// 		//20210929 codeby：chenwei 迁移代码到此合适位置
 		WCoord invitePos = invitePlayer->getPosition();
// 		bool isReverse = (act != 23 && act != 24);
// 		WCoord acceptPointPos = invitePlayer->getSkinActTargetPos(act, invitePos, isReverse);  //20210928 codeby:chenwei 抽取获取接收方位置到方法
 		Rainbow::Vector3f  dir = invitePos.toVector3();
 		dir = MINIW::Normalize(dir);
 		float yaw;
		float pitch;
 		Direction2PitchYaw(&yaw, &pitch, dir);
// 		acceptPlayer->getLocoMotion()->setMoveDir(dir);
// 		acceptPlayer->setMoveControlYaw(invitePlayer->getLocoMotion()->m_RotateYaw);
// 		//acceptPlayer->gotoPos(acceptPlayer->getWorld(), acceptPointPos, true);	//, invitePlayer->getLocoMotion()->m_RotateYaw, 0.0f
// 		//acceptPlayer->getLocoMotion()->gotoPosition(acceptPointPos, invitePlayer->getLocoMotion()->m_RotateYaw, 0.0f);
// 		acceptPlayer->getLocoMotion()->m_TickPosition.beginTick(acceptPointPos);
			//acceptPlayer->setFaceYaw(yaw);
// 	}
		if (acceptPlayer->isNewMoveSyncSwitchOn())
		{
			acceptPlayer->setMoveControlYaw(yaw);
			acceptPlayer->setMoveControlPitch(pitch);
		}
		else
		{
			acceptPlayer->setFaceYaw(yaw);
		}

	//20211009 codeby:chenwei 播放音效
	const PlayActDef *def = GetDefManagerProxy()->getPlayActDef(act);
	if (def)
	{
		auto soundComp1 = invitePlayer->getSoundComponent();
		if (soundComp1)
		{
			soundComp1->playSoundByTrigger(def->Sound.c_str(), 1.0, 1.0, false, false);
		}
		auto soundComp2 = acceptPlayer->getSoundComponent();
		if (soundComp2)
		{
			soundComp2->playSoundByTrigger(def->Sound.c_str(), 1.0, 1.0, false, false);
		}
	}

	setActCallFunc(inviteBody, false);
	setActCallFunc(acceptBody, true);
}
//2021-09-30 codeby:chenwei 处理邀请方和被邀请方动作设置
void MpGameSurviveNetHandler::handleStopSkinAct2Client(const PB_PACKDATA_CLIENT &pkg) //PB_ACTOR_STOP_SKIN_ACT_HC
{
	PB_ActorStopSkinActHC actorStopSkinActHC;
	actorStopSkinActHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	auto stopSkinAct = [this](int uin)
	{
		ClientPlayer *player = uin2Player(uin);

		if (player && player->getBody())
		{
			ActorBody* playerBody = player->getBody();
			int oldAct = playerBody->getActID();
			playerBody->setAct(-1);
			playerBody->setCurAnim(-1, 0);
			player->stopMotion(30000);
			player->setSkinPartnerUin(NULL);

			const PlayActDef *def = GetDefManagerProxy()->getPlayActDef(oldAct);
			if (def)
			{
				auto soundComp = player->getSoundComponent();
				if (soundComp)
				{
					soundComp->stopSoundByTrigger(def->Sound.c_str(), true);
				}
			}
		}
		//20211012 codeby:chenwei 停止动画时恢复视角
		if (g_pPlayerCtrl && uin == g_pPlayerCtrl->getUin())
		{
			g_pPlayerCtrl->recoverActView();
		}
	};

	stopSkinAct(actorStopSkinActHC.actorid1());
	stopSkinAct(actorStopSkinActHC.actorid2());
}



void MpGameSurviveNetHandler::handlePlayerSkin2Client(const PB_PACKDATA_CLIENT &pkg)
{
	WorldManager *worldMgr = m_root->getWorldMgr();
	if (!worldMgr)
	{
		LOG_INFO("WorldMgr == NULL");
		return;
	}
	PB_PlayerBriefInfo playerBriefInfo;
	playerBriefInfo.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	char custom[256] = "";
	char custommodel[256] = "";
	MyStringCpy(custom, sizeof(custom), playerBriefInfo.customjson().c_str());
	MyStringCpy(custommodel, sizeof(custommodel), playerBriefInfo.custommodel().c_str());
	worldMgr->updatePlayerSkin(playerBriefInfo.uin(), playerBriefInfo.playerindex(), custom, custommodel);
	ClientPlayer *player = checkPlayerByMsg2Host(playerBriefInfo.uin());
	if (player)
	{
		auto nickName = playerBriefInfo.nickname().c_str();
		auto teamValue = playerBriefInfo.teamid();
		auto body = player->getBody();
		if (body)
		{
			body->setDispayName(nickName, teamValue);
		}
		player->m_originSkinId = PlayerIndex2Skin(playerBriefInfo.playerindex());
	}
}

EXPORT_SANDBOXGAME extern void MeasureDistance_ShowText(World *pworld, const WCoord &blockpos, const WCoord &descPos);

void MpGameSurviveNetHandler::handleMeasureDistance2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_MeasureDistanceHC measuredistanceHC;
	measuredistanceHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WorldManager *worldMgr = m_root->getWorldMgr();
	if (!worldMgr)
	{
		return;
	}
	//ClientPlayer *player = uin2Player(measuredistanceHC.uin());
	World *pworld = worldMgr->getWorld(measuredistanceHC.mapid());

	if (true)
	{
		WCoord blockpos = MPVEC2WCoord(measuredistanceHC.blockpos());

		for(int i = 0; i < measuredistanceHC.findpos_size(); i++)
		{
			WCoord pos = MPVEC2WCoord(measuredistanceHC.findpos(i));
			MeasureDistance_ShowText(pworld, blockpos, pos);
		}
	}
}

void MpGameSurviveNetHandler::handleBluePrintPreBlock2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_BluePrintPreBlockHC bluePrintPreBlockHC;
	bluePrintPreBlockHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WorldManager *worldMgr = m_root->getWorldMgr();
	if (!worldMgr)
		return;

	World *pworld = worldMgr->getWorld(bluePrintPreBlockHC.mapid());
	if (!pworld)
		return;

	WCoord blockPos = MPVEC2WCoord(bluePrintPreBlockHC.blockpos());
	auto container = dynamic_cast<ContainerBuildBluePrint*>(pworld->getContainerMgr()->getContainer(blockPos));
	if (container)
	{
		container->attatchMeshByClient(&bluePrintPreBlockHC.preblocks());
	}
}

void MpGameSurviveNetHandler::handlePlayerBodyColor2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerBodyColorHC playerBodyColorHC;
	playerBodyColorHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(playerBodyColorHC.uin());
	if (player)
	{
		player->onHandlePlayerBodyColor2Client(playerBodyColorHC);
	}
}

void MpGameSurviveNetHandler::handleCustomModelPre2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_CustomModelPrepareCH customModelPreCH;
	PB_CustomModelPrepareHC customModelPreHC;
	customModelPreHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);	
	std::string filename = customModelPreHC.filename().c_str();
	int index = customModelPreHC.index();

	core::string cache_name = "data/http/modelcache/";
	cache_name += filename;

	customModelPreCH.set_index(index);
	
	if(GetFileManager().IsFileExistWritePath(cache_name.c_str()))
	{
		customModelPreCH.set_havefile(true);
	}
	else
	{
		customModelPreCH.set_havefile(false);
	}
	GetGameNetManagerPtr()->sendToHost(PB_CUSTOM_MODEL_PRE_CH, customModelPreCH);
}

void MpGameSurviveNetHandler::handleCustomModel2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_CustomModelHC customModelHC;
	
	if (customModelHC.ParseFromArray(pkg.MsgData, pkg.ByteSize) && CustomModelMgr::GetInstancePtr()) {
		CustomModelMgr::GetInstancePtr()->bgLoadCustomModelFromHost(customModelHC);
	}
}

void MpGameSurviveNetHandler::handleCustomItemIDs2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_CustomItemIDsHC customItemIDsHC;
	customItemIDsHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	int size = customItemIDsHC.customitemids_size();
	if (customItemIDsHC.customtypes_size() != size || customItemIDsHC.custommodelfilenames_size() != size || customItemIDsHC.custommodelclassnames_size() != size || customItemIDsHC.involvedids_size() != size)
	{
		return;
	}

	for (int i = 0; i < customItemIDsHC.customitemids_size(); i++)
	{
		//auto *def = BlockDefCsv::getInstance()->add(customItemIDsHC.customitemids(i));
		auto type = customItemIDsHC.customtypes(i);
		if(type == ACTOR_MODEL)
			GetDefManagerProxy()->addDefByCustomModel(customItemIDsHC.customitemids(i), customItemIDsHC.customtypes(i), customItemIDsHC.custommodelfilenames(i), "", "", Rainbow::Vector3f(0, 0, 0), customItemIDsHC.involvedids(i));
		else
			GetDefManagerProxy()->addDefByCustomModel(customItemIDsHC.customitemids(i), customItemIDsHC.customtypes(i), customItemIDsHC.custommodelfilenames(i), "", "", Rainbow::Vector3f(0, 0, 0), customItemIDsHC.involvedids(i));
		if (customItemIDsHC.custommodelfolderindexs_size() == customItemIDsHC.customitemids_size())
		{
			CustomModelMgr::GetInstancePtr()->addCustomItemData(customItemIDsHC.customitemids(i), customItemIDsHC.custommodelfilenames(i), customItemIDsHC.custommodelclassnames(i), customItemIDsHC.customtypes(i), customItemIDsHC.involvedids(i), customItemIDsHC.custommodelfolderindexs(i));
		}
		else
		{
			CustomModelMgr::GetInstancePtr()->addCustomItemData(customItemIDsHC.customitemids(i), customItemIDsHC.custommodelfilenames(i), customItemIDsHC.custommodelclassnames(i), customItemIDsHC.customtypes(i), customItemIDsHC.involvedids(i));
		}
		if (ResourceCenter::GetInstancePtr())
		{
			if (customItemIDsHC.custommodelfolderindexs_size() == customItemIDsHC.customitemids_size())
			{
				ResourceCenter::GetInstancePtr()->addOneResToClass(customItemIDsHC.custommodelfilenames(i), false, customItemIDsHC.custommodelfolderindexs(i));
				//CustomModelMgr::GetInstancePtr()->addOneModelToClass(customItemIDsHC.custommodelfilenames(i), false, customItemIDsHC.custommodelfolderindexs(i));
			}
			else
			{
				ResourceCenter::GetInstancePtr()->addOneResToClass(customItemIDsHC.custommodelfilenames(i), false);
				//CustomModelMgr::GetInstancePtr()->addOneModelToClass(customItemIDsHC.custommodelfilenames(i), false);
			}
		}
	}
}

void MpGameSurviveNetHandler::handlePlayerSpawnPoint2Client(const PB_PACKDATA_CLIENT &pkg )
{
	PB_PlayerSpawnPointHC playerSpawnPointHC;
	playerSpawnPointHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	//WCoord *spawnpt = new WCoord(playerSpawnPointHC.x(), playerSpawnPointHC.y(), playerSpawnPointHC.z());
	ClientPlayer *player = uin2Player(playerSpawnPointHC.uin());

	if (player && player->getWorld())
	{
		WCoord spawnpoint = WCoord(0, -1, 0);
		WCoord revivepoint = WCoord(0, -1, 0);
		int mapid = playerSpawnPointHC.has_mapid() ? playerSpawnPointHC.mapid() : player->getWorld()->getCurMapID();

		if ( player )//spawnpt && 
		{
			SandboxContext context;
			context.SetData_UserObject("spawn", spawnpoint);
			context.SetData_UserObject("revive", revivepoint);
			context.SetData_Number("mapid", mapid);
			SandboxResult result = player->Event().Emit("revive_getAccountWorldPoint", context);

			if (!result.IsExecSuccessed()) {//&& !player->getAccountWorldPoint(mapid, spawnpoint, revivepoint)
				//player->setAccountWorldPoint(mapid, *spawnpt);
				SandboxContext setcontext;
				setcontext.SetData_UserObject("spawn", WCoord(playerSpawnPointHC.x(), playerSpawnPointHC.y(), playerSpawnPointHC.z()));
				setcontext.SetData_UserObject("revive", WCoord(0, -1, 0));
				setcontext.SetData_Number("mapid", mapid);
				player->Event().Emit("revive_setAccountWorldPoint", setcontext);
			}
		}
	}

	//delete spawnpt;
}

void MpGameSurviveNetHandler::handleCustomModelClass2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_CustomModelClassHC customModelClassHC;
	customModelClassHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (ResourceCenter::GetInstancePtr())
		ResourceCenter::GetInstancePtr()->setClassInfoByHost(&customModelClassHC);
}

// 传送点更新 h->c 
void MpGameSurviveNetHandler::handleTransfer2Client(const PB_PACKDATA_CLIENT &pkg)
{
	SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Protocol_Message_Dispatcher_Client",
			SandboxContext(nullptr).SetData_Number("msgCode", (int)PB_TRANSFER_RECORD_HC).
			SetData_Userdata("PB_PACKDATA_CLIENT", "packagedata", (void*)&pkg));

	/*PB_TransferRecordHC oneTransferRecord;
	oneTransferRecord.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	TransferMgr* transferModule = GET_SUB_SYSTEM(TransferMgr);
	if (transferModule)
	{
		if (oneTransferRecord.isedit())
			transferModule->updateTransferDataByOthers(oneTransferRecord, true);
		else
			transferModule->deleteTransferDataByOthers(oneTransferRecord, true);
	}*/
}
void MpGameSurviveNetHandler::handleTransferAddDel2Client(const PB_PACKDATA_CLIENT &pkg)
{
	SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Protocol_Message_Dispatcher_Client",
			SandboxContext(nullptr).SetData_Number("msgCode", (int)PB_TRANSFER_ADD_DEL_HC).
			SetData_Userdata("PB_PACKDATA_CLIENT", "packagedata", (void*)&pkg));
	/*PB_TransferAddDelHC addDelRecord;
	addDelRecord.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	TransferMgr* transferModule = GET_SUB_SYSTEM(TransferMgr);
	if (transferModule)
	{
		transferModule->addOrDelTransferDefByHost(addDelRecord);
	}*/
}
void MpGameSurviveNetHandler::handleTransferStatus2Client(const PB_PACKDATA_CLIENT &pkg)
{
	SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Protocol_Message_Dispatcher_Client",
			SandboxContext(nullptr).SetData_Number("msgCode", (int)PB_TRANSFER_STATUS_HC).
			SetData_Userdata("PB_PACKDATA_CLIENT", "packagedata", (void*)&pkg));
	/*PB_TransferNameTipHC transferStatusHC;
	transferStatusHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	
	TransferMgr* transferModule = GET_SUB_SYSTEM(TransferMgr);
	if (transferModule)
	{
		transferModule->updateTransferStatusByHost(transferStatusHC);
	}*/
}
void MpGameSurviveNetHandler::handleTransferData2Client(const PB_PACKDATA_CLIENT &pkg)
{
	SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Protocol_Message_Dispatcher_Client",
			SandboxContext(nullptr).SetData_Number("msgCode", (int)PB_TRANSFER_DATA_HC).
			SetData_Userdata("PB_PACKDATA_CLIENT", "packagedata", (void*)&pkg));
	/*PB_TransferDataHC transferDataHC;
	transferDataHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	
	TransferMgr* transferModule = GET_SUB_SYSTEM(TransferMgr);
	if (transferModule)
	{
		transferModule->loadTransferDataByHost(transferDataHC);
	}*/
}

void MpGameSurviveNetHandler::handleTransferUI2Client(const PB_PACKDATA_CLIENT &pkg)
{
	SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Protocol_Message_Dispatcher_Client",
			SandboxContext(nullptr).SetData_Number("msgCode", (int)PB_ACTOR_TRANSFER_HC).
			SetData_Userdata("PB_PACKDATA_CLIENT", "packagedata", (void*)&pkg));
	/*PB_OpenUIHC transferUIHC;
	transferUIHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	TransferMgr* transferModule = GET_SUB_SYSTEM(TransferMgr);
	if (transferModule)
	{
		transferModule->notifyTransferUIByHost(transferUIHC);
	}*/
}

void MpGameSurviveNetHandler::handleSyncLoveAmbassadorIcon2Client(const PB_PACKDATA_CLIENT & pkg)
{
	PB_MobDisplayData data;
	data.ParseFromArray(pkg.MsgData,pkg.ByteSize);
	int mobId = data.mobid();
	int itemId = data.itemid();
	int itemCount = data.itemcount();
	int animId = data.animid();
	if (itemId != 0)
	{	
		char tmpbuf1[64];
		char tmpbuf2[64];
		sprintf(tmpbuf1, "F%d_SetCurNeedItemId", mobId);
		sprintf(tmpbuf2, "F%d_SetCurNeedItemCount", mobId);
		MINIW::ScriptVM::game()->callFunction(tmpbuf1, "i",itemId);
		MINIW::ScriptVM::game()->callFunction(tmpbuf2, "i",itemCount);
	}
	if (g_pPlayerCtrl && g_pPlayerCtrl->getWorld() && g_pPlayerCtrl->getWorld()->getActorMgr())
	{
		g_pPlayerCtrl->getWorld()->getActorMgr()->ToCastMgr()->updateLoveAmbassadorIcon(mobId,itemId,itemCount,animId);
	}
}

void MpGameSurviveNetHandler::handleNpcShopRespGetInfo2Client(const PB_PACKDATA_CLIENT & pkg)
{
	if (!g_pPlayerCtrl) { return; }

	PB_RespNpcShopInfoHC data;
	data.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	g_pPlayerCtrl->respNpcShopInfo(data.npcshopinfo());
}

void MpGameSurviveNetHandler::handleNpcShopNotifyBuySku2Client(const PB_PACKDATA_CLIENT &pkg)
{
	if (!g_pPlayerCtrl) { return; }

	PB_NotifyNpcShopBuySkuHC data;
	data.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	bool isSuccess = (data.ret() == 0 && data.uin() == GetClientInfoProxy()->getUin());
	g_pPlayerCtrl->updateNpcShopSkuInfo(data.shopid(), data.skuid(), data.leftnum(), data.endtime(), data.buycount(), isSuccess);
}

void MpGameSurviveNetHandler::handleOpenEditActorModel2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_OpenEditActorModelHC openEditActorModelHC;
	openEditActorModelHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WorldManager *worldMgr = m_root->getWorldMgr();
	if(worldMgr == NULL) return;
	World *pworld = worldMgr->getWorld(openEditActorModelHC.mapid());
	if (!pworld)
		return;

	if (!CustomModelMgr::GetInstancePtr())
		return;


	WorldContainer *container = pworld->getContainerMgr()->getContainer(WCoord(openEditActorModelHC.containerpos().x(), openEditActorModelHC.containerpos().y(), openEditActorModelHC.containerpos().z()));
	if (container)
		container->syncCustomActorModelData(g_pPlayerCtrl);

	GetGameEventQue().postOpenEditActorModel();
}

void MpGameSurviveNetHandler::handleCloseEditActorModel2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_CloseEditActorModelHC closeEditActorModelHC;
	closeEditActorModelHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WorldManager *worldMgr = m_root->getWorldMgr();
	if(worldMgr == NULL) return;
	World *pworld = worldMgr->getWorld(closeEditActorModelHC.mapid());
	if (!pworld)
		return;


	CustomActorModelData actorModelData;
	for (size_t i = 0; i < (size_t)closeEditActorModelHC.bonemodels_size(); i++)
	{
		std::string boneName = closeEditActorModelHC.bonemodels(i).bonename();

		std::vector<CustomAvatarModelData> avatarModelDatas;
		avatarModelDatas.clear();
		for (size_t j = 0; j < (size_t)closeEditActorModelHC.bonemodels(i).avatarmodels_size(); j++)
		{
			auto &srcData = closeEditActorModelHC.bonemodels(i).avatarmodels(j);
			CustomAvatarModelData avatarModelData;

			avatarModelData.modelfilename = srcData.modelfilename();
			avatarModelData.scale = srcData.scale();
			avatarModelData.yaw = (float)srcData.yaw();
			avatarModelData.pitch = (float)srcData.pitch();
			if(srcData.has_roll())
				avatarModelData.roll = (float)srcData.roll();
			avatarModelData.offset_x = srcData.offsetpos().x();
			avatarModelData.offset_y = srcData.offsetpos().y();
			avatarModelData.offset_z = srcData.offsetpos().z();
			if(srcData.has_newrotatemode())
				avatarModelData.newrotatemode = srcData.newrotatemode();

			avatarModelDatas.push_back(avatarModelData);
		}

		actorModelData.models[boneName] = avatarModelDatas;
	}

	actorModelData.type = (ACTOR_MODEL_TYPE)closeEditActorModelHC.modeltype();
	actorModelData.modelmark = closeEditActorModelHC.modelmark();
	if(closeEditActorModelHC.has_skindisplay())
		actorModelData.skindisplay = closeEditActorModelHC.skindisplay();


	if (closeEditActorModelHC.containerpos().y() >= 0)
	{
		WCoord pos(closeEditActorModelHC.containerpos().x(), closeEditActorModelHC.containerpos().y(), closeEditActorModelHC.containerpos().z());
		WorldContainer *container = pworld->getContainerMgr()->getContainerExt(pos);
		if (container)
			container->updateCustomActorModelData(actorModelData);
	}

	if(CustomModelMgr::GetInstancePtr())
	{
		std::string modelName = closeEditActorModelHC.modelname();
		if (modelName != "")
		{
			auto customItem = CustomModelMgr::GetInstancePtr()->getCustomItem(actorModelData.modelmark);
			if (customItem)
			{
				auto itemDef = GetDefManagerProxy()->getItemDef(customItem->involvedid);
				if (itemDef)
				{
					itemDef->Name = modelName.c_str();
				}

				auto monsterDef = GetDefManagerProxy()->getMonsterDef(customItem->itemid);
				if (monsterDef)
				{
					monsterDef->Name = modelName.c_str();
				}
			}
		}

		CustomModelMgr::GetInstancePtr()->addCustomActorBySync(actorModelData);
	}
}

void MpGameSurviveNetHandler::handleCustomActorModelData2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_CustomActorModelDataHC customActorModelDataHC;
	customActorModelDataHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (!CustomModelMgr::GetInstancePtr())
		return;

	for (size_t i = 0; i < (size_t)customActorModelDataHC.modeldatas_size(); i++)
	{
		CustomActorModelData actorModelData;
		actorModelData.models.clear();

		for (size_t j = 0; j < (size_t)customActorModelDataHC.modeldatas(i).bonemodels_size(); j++)
		{
			std::string boneName = customActorModelDataHC.modeldatas(i).bonemodels(j).bonename();

			std::vector<CustomAvatarModelData> avatarModelDatas;
			avatarModelDatas.clear();
			for (size_t k = 0; k < (size_t)customActorModelDataHC.modeldatas(i).bonemodels(j).avatarmodels_size(); k++)
			{
				auto &srcData = customActorModelDataHC.modeldatas(i).bonemodels(j).avatarmodels(k);
				CustomAvatarModelData avatarModelData;

				avatarModelData.modelfilename = srcData.modelfilename();
				avatarModelData.scale = srcData.scale();
				avatarModelData.yaw = (float)srcData.yaw();
				avatarModelData.pitch = (float)srcData.pitch();
				if(srcData.has_roll())
					avatarModelData.roll = (float)srcData.roll();
				avatarModelData.offset_x = (short)srcData.offsetpos().x();
				avatarModelData.offset_y = (short)srcData.offsetpos().y();
				avatarModelData.offset_z = (short)srcData.offsetpos().z();

				if (srcData.has_newrotatemode())
					avatarModelData.newrotatemode = srcData.newrotatemode();

				avatarModelDatas.push_back(avatarModelData);
			}

			actorModelData.models[boneName] = avatarModelDatas;
		}

		actorModelData.modelmark = customActorModelDataHC.modeldatas(i).modelmark();
		actorModelData.type = (ACTOR_MODEL_TYPE)customActorModelDataHC.modeldatas(i).type();
		if (customActorModelDataHC.modeldatas(i).has_skindisplay())
			actorModelData.skindisplay = customActorModelDataHC.modeldatas(i).skindisplay();
		if (customActorModelDataHC.modeldatas(i).has_authuin())
		{
			actorModelData.authuin = customActorModelDataHC.modeldatas(i).authuin();
		}

		std::string modelName = customActorModelDataHC.modeldatas(i).modelname();
		actorModelData.modelname = modelName;
		if (modelName != "")
		{
			auto customItem = CustomModelMgr::GetInstancePtr()->getCustomItem(actorModelData.modelmark);
			if (customItem)
			{
				auto itemDef = GetDefManagerProxy()->getItemDef(customItem->involvedid);
				if (itemDef)
				{
					itemDef->Name = modelName.c_str();
				}

				auto monsterDef = GetDefManagerProxy()->getMonsterDef(customItem->itemid);
				if (monsterDef)
				{
					monsterDef->Name = modelName.c_str();
				}
			}
		}
		
		CustomModelMgr::GetInstancePtr()->addCustomActorBySync(actorModelData);
	}
}
void MpGameSurviveNetHandler::handleVehiclePreBlock2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_VehiclePreBlockHC vehiclePreBlockHC;
	vehiclePreBlockHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WorldManager *worldMgr = m_root->getWorldMgr();
	if (!worldMgr)
		return;

	World *pworld = worldMgr->getWorld(vehiclePreBlockHC.mapid());
	if (!pworld)
		return;

	WCoord blockPos = MPVEC2WCoord(vehiclePreBlockHC.blockpos());
	auto container = dynamic_cast<ContainerWorkshop*>(pworld->getContainerMgr()->getContainer(blockPos));
	if (container)
	{
		container->attatchMeshByHost(&vehiclePreBlockHC.preblocks());
		if (vehiclePreBlockHC.has_attrinfo())
		{
			std::string info = vehiclePreBlockHC.attrinfo();
			MINIW::ScriptVM::game()->callFunction("ShowPreVehicleAttrib", "s", info.c_str());
		}
	}
}
void MpGameSurviveNetHandler::handleVehicleAllItemid2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_VehicleItemIdHC vehicleItemHC;
	vehicleItemHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	std::vector<int> itemid;
	for (int i = 0; i < vehicleItemHC.itemid().size(); i++)
	{
		itemid.push_back(vehicleItemHC.itemid(i));
	}

	if (itemid.size() <= 0)
		return;

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_syncPlayerAllVehicleItemidByHost",
		SandboxContext(nullptr).SetData_Usertype<std::vector<int>>("itemids", &itemid));
}
void MpGameSurviveNetHandler::handleVehicleOneItemid2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_VehicleItemIdHC vehicleItemHC;
	vehicleItemHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WorldManager *worldMgr = m_root->getWorldMgr();
	if (!worldMgr)
		return;

	int itemid = vehicleItemHC.itemid(0);
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_syncPlayerOneVehicleItemidByHost",
		SandboxContext(nullptr)
		.SetData_Number("itemid", itemid));
}

void MpGameSurviveNetHandler::handleVehicleAttribChange2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_VehicleAttribChangeHC vehicleAttribChangeHC;
	vehicleAttribChangeHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ActorVehicleAssemble *vehicle = dynamic_cast<ActorVehicleAssemble *>(objId2ActorOnClient(vehicleAttribChangeHC.objid()));
	if (vehicle)
	{
		//同步油耗
		if (vehicleAttribChangeHC.has_fuel())
		{
			int dfuel = vehicleAttribChangeHC.fuel();
			int index = vehicleAttribChangeHC.partindex();

			WCoord pos((index >> 10), (index >> 5) & 0x1f, (index & 0x1f));
			vehicle->setFuelWithBlockPos(dfuel, pos, false, true);
		}
		//同步行驶速度
		if (vehicleAttribChangeHC.has_actualspeed()&& vehicleAttribChangeHC.has_showspeed())
		{
			int actualspeed = vehicleAttribChangeHC.actualspeed();
			int showspeed = vehicleAttribChangeHC.showspeed();
			vehicle->syncVehicleSpeed(actualspeed, showspeed);
		}
		//同步引擎转速
		if (vehicleAttribChangeHC.has_enginerotationspeed())
		{
			float speed = vehicleAttribChangeHC.enginerotationspeed();
			vehicle->syncEngintRotationSpeed(speed);
		}
		//同步引擎状态
		if (vehicleAttribChangeHC.has_enginestate())
		{
			int state = vehicleAttribChangeHC.enginestate();
			vehicle->syncEngineState((VEHICLE_ENGINESOUND_STATE)state, true);
		}
		//同步航天推进器状态
		for (int i = 0; i < vehicleAttribChangeHC.strusterspower_size(); i++)
		{
			PB_VehicleSTrustersPowerLevel STrusterData = vehicleAttribChangeHC.strusterspower(i);
			WCoord pos = MPVEC2WCoord(STrusterData.position());
			MINIW::SThrusterParam * pSTruster = vehicle->getSThrusterParamWithPos(pos);
			pSTruster->m_nChangeState = STrusterData.powerlevel();
			pSTruster->m_fCurPower = (float)STrusterData.curpower();
		}
	}
}
void MpGameSurviveNetHandler::handleWorkshopItemInfo2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_WorkshopItemInfoHC workshopItemInfoHC;
	workshopItemInfoHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WorldManager *worldMgr = m_root->getWorldMgr();
	if (!worldMgr)
		return;

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_setWorkShopItemInfoByHost",
		SandboxContext(nullptr)
		.SetData_UserObject("workshopItemInfoHC", workshopItemInfoHC));
}

void MpGameSurviveNetHandler::handleWorkshopBuild2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_WorkshopBuildHC workshopbuildHC;
	workshopbuildHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WorldManager *worldMgr = m_root->getWorldMgr();
	if (!worldMgr)
		return;
	World *pworld = worldMgr->getWorld(workshopbuildHC.mapid());
	if(pworld)
	{
		WCoord blockPos = MPVEC2WCoord(workshopbuildHC.containerpos());

		
		ContainerWorkshop *container = dynamic_cast<ContainerWorkshop *>(pworld->getContainerMgr()->getContainer(blockPos));
		if(container)
		{
			if (!workshopbuildHC.isbuild())
			{
				container->calculateConnectMap();
				container->genVehicleBlockNode();
			}
			container->setStart(workshopbuildHC.isbuild());
		}
	}
}

void MpGameSurviveNetHandler::handleVehicleAssembleBlockAll2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_VehicleAssembleBlockAllHC vehicleAssembleBlockAllHC;
	vehicleAssembleBlockAllHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ActorVehicleAssemble *vehicle = dynamic_cast<ActorVehicleAssemble *>(objId2ActorOnClient(vehicleAssembleBlockAllHC.objid()));
	if (vehicle)
	{
		const char* chunkBuffer = vehicleAssembleBlockAllHC.blobdetail().c_str(); 
		size_t destlen = vehicleAssembleBlockAllHC.unziplen(); 
		size_t srcLen =	vehicleAssembleBlockAllHC.bloblen();
		int compresstype = destlen >> 28;
		destlen &= (1 << 28) - 1;
		if (destlen > 4 * 1024 * 1024)
		{
			assert(0);
			return; //解压缩数据不能大于2M, 否则肯定是数据错误
		}
		unsigned char *rawdata = new (std::nothrow) unsigned char[destlen];
		if(rawdata == NULL)  return;																	   
		Rainbow::CompressTool ctool(compresstype);

		if (!ctool.decompress(rawdata, destlen, chunkBuffer, srcLen))
		{
			assert(0);
			LOG_WARNING("uncompress chunk save blob failed");
			OGRE_DELETE_ARRAY(rawdata);
			return;
		}
		vehicle->syncAllBlock(rawdata, destlen, vehicleAssembleBlockAllHC.blockversion());
		OGRE_DELETE_ARRAY(rawdata);
		//同步载具位置信息
		VehicleSyncPosDesc desc;
		PB_VehiclePosDesc descPB;
		std::vector<VehicleSyncPosDesc> chassisServerPos;
		std::vector<VehicleSyncPosDesc> wheelServerPos;

		for (int i = 0; i < vehicleAssembleBlockAllHC.chassispos_size(); i++)
		{
			descPB = vehicleAssembleBlockAllHC.chassispos(i);
			desc.m_PPosition = MPVEC2WCoord(descPB.position());
			desc.m_RotateQuat = MPVEC2Quaternion(descPB.rotatequat());
			chassisServerPos.push_back(desc);
		}

		for (int i = 0; i < vehicleAssembleBlockAllHC.wheelpos_size(); i++)
		{
			descPB = vehicleAssembleBlockAllHC.wheelpos(i);
			desc.m_PPosition = MPVEC2WCoord(descPB.position());
			desc.m_RotateQuat = MPVEC2Quaternion(descPB.rotatequat());
			wheelServerPos.push_back(desc);
		}
		int isDouble = g_pPlayerCtrl == vehicle->getRiddenByActor() ? 2 : 1;
		int ticks = 3;
		MpGameSurvive* mp = dynamic_cast<MpGameSurvive*>(ClientGameManager::getInstance()->getCurGame());
		if (mp)
		{
			ticks += mp->getNetDelayTick();
			ticks = ticks >= 6 ? ticks * 5 * isDouble : ticks * isDouble;
		}
		vehicle->syncServerPos(chassisServerPos, wheelServerPos, ticks, 3);

		if (chassisServerPos.size())
		{
			vehicle->moveToPosition(WCoord(chassisServerPos[0].m_PPosition.x, chassisServerPos[0].m_PPosition.y, chassisServerPos[0].m_PPosition.z), chassisServerPos[0].m_RotateQuat, ticks);
		}
	}
}

void MpGameSurviveNetHandler::handleVehicleAssembleBlockUpdate2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_VehicleAssembleBlockUpdateHC vehicleAssembleBlockUpdateHC;
	vehicleAssembleBlockUpdateHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ActorVehicleAssemble *vehicle = dynamic_cast<ActorVehicleAssemble *>(objId2ActorOnClient(vehicleAssembleBlockUpdateHC.objid()));
	if (vehicle)
	{
		std::vector<unsigned int> blockpos;
		std::vector<unsigned int> blockinfo;
		std::vector<unsigned int> blockdata;
		for (int i = 0; i < vehicleAssembleBlockUpdateHC.blockinfo_size(); i++)
		{
			const ::game::hc::AssembleBlockInfo& info	= vehicleAssembleBlockUpdateHC.blockinfo(i);
			blockpos.push_back(info.block());
			unsigned int blockalldata = Block::changeBlockNewFormatData(info.data(), info.blockex());
			blockdata.push_back(blockalldata);
			blockinfo.push_back(info.info());
		}

		const FBSave::ChunkContainers *s = NULL;
		if (vehicleAssembleBlockUpdateHC.containerbuf().length() > 0)
		{
			s = flatbuffers::GetRoot<FBSave::ChunkContainers>(vehicleAssembleBlockUpdateHC.containerbuf().c_str());
		}
		vehicle->syncBlock(blockpos, blockdata, blockinfo, s);
		//同步载具位置信息
		VehicleSyncPosDesc desc;
		PB_VehiclePosDesc descPB;
		std::vector<VehicleSyncPosDesc> chassisServerPos;
		std::vector<VehicleSyncPosDesc> wheelServerPos;

		for (int i = 0; i < vehicleAssembleBlockUpdateHC.chassispos_size(); i++)
		{
			descPB = vehicleAssembleBlockUpdateHC.chassispos(i);
			desc.m_PPosition = MPVEC2WCoord(descPB.position());
			desc.m_RotateQuat = MPVEC2Quaternion(descPB.rotatequat());
			chassisServerPos.push_back(desc);
		}

		for (int i = 0; i < vehicleAssembleBlockUpdateHC.wheelpos_size(); i++)
		{
			descPB = vehicleAssembleBlockUpdateHC.wheelpos(i);
			desc.m_PPosition = MPVEC2WCoord(descPB.position());
			desc.m_RotateQuat = MPVEC2Quaternion(descPB.rotatequat());
			wheelServerPos.push_back(desc);
		}
		int isDouble = g_pPlayerCtrl == vehicle->getRiddenByActor() ? 2 : 1;
		int ticks = 3;
		MpGameSurvive* mp = dynamic_cast<MpGameSurvive*>(ClientGameManager::getInstance()->getCurGame());
		if (mp)
		{
			ticks += mp->getNetDelayTick();
			ticks = ticks >= 6 ? ticks * 5 * isDouble : ticks * isDouble;
		}
		vehicle->syncServerPos(chassisServerPos, wheelServerPos, ticks, 3);

		if (chassisServerPos.size())
		{
			vehicle->moveToPosition(WCoord(chassisServerPos[0].m_PPosition.x, chassisServerPos[0].m_PPosition.y, chassisServerPos[0].m_PPosition.z), chassisServerPos[0].m_RotateQuat, ticks);
		}
	}
}

void MpGameSurviveNetHandler::handleTriggerTimer2Client(const PB_PACKDATA_CLIENT &pkg)
{
}

void MpGameSurviveNetHandler::handleGameRule2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_GameRuleHC data;
	data.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (data.has_ruleid() && data.has_optionid() && data.has_value())
	{
		if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr)
			GetWorldManagerPtr()->m_RuleMgr->setGameRule(data.ruleid(), data.optionid(), data.value());
	}
}

void MpGameSurviveNetHandler::handlePlayerCameraRotate2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerCameraRotateHC playerCameraRotateHC;
	playerCameraRotateHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (g_pPlayerCtrl)
		g_pPlayerCtrl->rotateCamera(playerCameraRotateHC.yaw(), playerCameraRotateHC.pitch());
}

void MpGameSurviveNetHandler::handlePlayerChangeViewMode2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerChangeViewModeHC playerChangeViewModeHC;
	playerChangeViewModeHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (g_pPlayerCtrl)
	{
		if (playerChangeViewModeHC.clientmode())
		{
			g_pPlayerCtrl->changeViewMode(GetIWorldConfigProxy()->getGameData("view") - 1, playerChangeViewModeHC.lock(), true);
		}
		else
		{
			g_pPlayerCtrl->changeViewMode(playerChangeViewModeHC.viewmode(), playerChangeViewModeHC.lock());
			g_pPlayerCtrl->saveMsgChangeViewMode(playerChangeViewModeHC.viewmode(), playerChangeViewModeHC.lock());
		}
	}
}

void MpGameSurviveNetHandler::handlePlayerCanMove2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerCanMoveHC playerCanMoveHC;
	playerCanMoveHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (g_pPlayerCtrl)
		g_pPlayerCtrl->setActionAttrState(ENABLE_MOVE, playerCanMoveHC.canmove());
}

void MpGameSurviveNetHandler::handlePlayerCanControl2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerCanControlHC playerCanControlHC;
	playerCanControlHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (g_pPlayerCtrl)
		g_pPlayerCtrl->setCanControl(playerCanControlHC.cancontrol());
}

void MpGameSurviveNetHandler::handlePlayerSetAttr2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerSetAttrHC playerSetAttrHC;
	playerSetAttrHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (!g_pPlayerCtrl)
	{
		return;
	}	
	PlayerAttrib* playerAttr = g_pPlayerCtrl->getPlayerAttrib();
	if (!playerAttr)
	{
		return;
	}

	const int attrType = playerSetAttrHC.attrtype();
	const float val = playerSetAttrHC.val();
	//LOG_INFO("handlePlayerSetAttr2Client(): uin = %d | attrType = %d | value = %.2f", g_pPlayerCtrl->getUin(), attrType, val);

	if (attrType == ATTRT_MAX_HP)
		playerAttr->setMaxHP(val);
	else if(attrType == ATTRT_OVERFLOW_HP)
		playerAttr->setOverflowHP(val);
	else if(attrType == ATTRT_CUR_HUNGER)
		playerAttr->setFoodLevel(val);
	else if(attrType == ATTRT_CUR_THIRST)
		playerAttr->setThirst(val);
	else if (attrType == ATTRT_RADIATION)
		playerAttr->setRadiation(val);
	else if (attrType == ATTRT_TEMPERATURE)
		playerAttr->setFinalPosTemperature(val);
	//else if(attrType == ATTRT_CUR_HP)
	//	playerAttr->setHP(val);
	//else if(attrType == ATTRT_CUR_OVERFLOWABLE_HP)
	//	playerAttr->setHP(val, true);

	else if(attrType == ATTRT_MAX_STRENGTH)
		playerAttr->setMaxStrength(val);
	else if(attrType == ATTRT_OVERFLOW_STRENGTH)
		playerAttr->setOverflowStrength(val);
	else if(attrType == ATTRT_CUR_STRENGTH)
		playerAttr->setStrength(val);
	else if (attrType >= ATTRT_WALK_SPEED && attrType <= ATTRT_JUMP_POWER)
		playerAttr->setSpeedAtt(attrType - ATTRT_WALK_SPEED, val);

	else if(attrType == ATTRT_LEVELMODE_INCREMENT )
		playerAttr->addLevelModelExp((int)val);

	else if (attrType == ATTRT_ATK_MELEE)
		playerAttr->setAttackBaseLua(ATTACK_PUNCH, val);
	else if (attrType == ATTRT_ATK_REMOTE)
		playerAttr->setAttackBaseLua(ATTACK_RANGE, val);

	else if (attrType == ATTRT_DEF_MELEE)
		playerAttr->setArmorBaseLua(ATTACK_PUNCH, val);
	else if (attrType == ATTRT_DEF_REMOTE)
		playerAttr->setArmorBaseLua(ATTACK_RANGE, val);
	else if (attrType == ATTRT_ARMOR)
		playerAttr->setArmor(val, false);
	else if (attrType == ATTRT_PERSEVERANCE)
		playerAttr->setPerseverance(val, false);
}

void MpGameSurviveNetHandler::handlePlayerLevelMode2Client(const PB_PACKDATA_CLIENT& pkg)
{
	//接收主机发送过来给客机的等级经验信息
	PB_PlayerLevelModeHC playerLevelModeHC;
	playerLevelModeHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (g_pPlayerCtrl)
	{
		int nSumExp = playerLevelModeHC.sumexp();
		int nCurExp = playerLevelModeHC.curexp();
		int nCurLevel = playerLevelModeHC.curlevel();

		g_pPlayerCtrl->SetLevelMode(nSumExp, nCurExp, nCurLevel);
	}
}

void MpGameSurviveNetHandler::handleActionAttrState2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_ActionAttrStateHC actionAttrStateHC;
	actionAttrStateHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (g_pPlayerCtrl)
	{
		auto ActionAttrStateComp = g_pPlayerCtrl->getActionAttrStateComponent();
		if (ActionAttrStateComp)
		{
			ActionAttrStateComp->setAllActionAttrState(actionAttrStateHC.attr());
		}
		
	}
}

void MpGameSurviveNetHandler::handleCraftingQueueUpdate2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_CraftingQueueUpdateHC hc;
	hc.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (!g_pPlayerCtrl)
		return;
	auto craftingQueue = g_pPlayerCtrl->getCraftingQueue();
	if (!craftingQueue)
		return;
	craftingQueue->ClearQueue();
	for (const auto& task : hc.tasks()) {
		craftingQueue->Net2Client(task.crafting_id(), task.count(), task.ticks_per_item(),task.remaining_ticks());
	}
	GetGameEventQue().PostCraftingQueueChange();
}

void MpGameSurviveNetHandler::handlePlayerFreezing2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerFreezingHC playerFreezingHC;
	playerFreezingHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (g_pPlayerCtrl)
		g_pPlayerCtrl->setFreezing(playerFreezingHC.freezingflag());
}

void MpGameSurviveNetHandler::handlePlayerAttrScale2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerScaleHC playerScaleHC;
	playerScaleHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	
	long long objId = playerScaleHC.objid();
	float scale = playerScaleHC.scale();

	//生物(mob)
	ClientActor *actor = objId2ActorOnClient(objId);
	if(actor)
	{
		ActorLiving* living = dynamic_cast<ActorLiving*>(actor);
		if(living && scale > 0.0001)
		{
			living->setCustomScale(scale);
		}
	} 
	else  //玩家(player)
	{
		ClientPlayer *ccPlayer = uin2Player(playerScaleHC.uin());
		if (ccPlayer != NULL && scale > 0.0001)
		{
			ccPlayer->setCustomScale(scale);
		}
	}
}

void MpGameSurviveNetHandler::handleTriggerMusic2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_TriggerMusicHC playerMusicHC;
	playerMusicHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	
	ClientPlayer *curPlayer = uin2Player(playerMusicHC.objid());
	if (curPlayer != NULL)
	{
		int playState = playerMusicHC.playstate();
		if (playState == PLAYSTAT_PLAY) 
		{
			std::string musicName = playerMusicHC.name();
			float volume = playerMusicHC.volume();
			float pitch = playerMusicHC.pitch();
			bool isLoop = playerMusicHC.isloop();
			curPlayer->playMusicByTrigger(musicName.c_str(),volume,pitch,isLoop);
		}
		else if (playState == PLAYSTAT_STOP)
		{
			std::string musicName = playerMusicHC.name();
			curPlayer->stopMusicByTrigger(musicName.c_str());
		}
	}
}

void MpGameSurviveNetHandler::handleTriggerOpenStore2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_TriggerOpenStoreHC triggerOpenStoreHC;
	triggerOpenStoreHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *curPlayer = uin2Player((int)triggerOpenStoreHC.objid());
	if (curPlayer != NULL)
	{
		curPlayer->openDevStoreByTrigger();
	}
}

void MpGameSurviveNetHandler::handlePlayerNavigate2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerNavigateHC playerNavigateHC;
	playerNavigateHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (g_pPlayerCtrl)
	{
		WCoord targetPos = MPVEC2WCoord(playerNavigateHC.targetpos());
		g_pPlayerCtrl->tryMoveTo(targetPos.x, targetPos.y, targetPos.z, playerNavigateHC.speed(), playerNavigateHC.cancontrol(),false, playerNavigateHC.showtip());
	}
}
void MpGameSurviveNetHandler::handlePlayerFaceYaw2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerCommonSetHC playerfaceHC;
	playerfaceHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (g_pPlayerCtrl)
	{
		g_pPlayerCtrl->setFaceYaw(playerfaceHC.value(),false);
	}
}

void MpGameSurviveNetHandler::handleOpenEditFullyCustomModel2Client(const PB_PACKDATA_CLIENT &pkg)
{
	LOG_INFO("handleOpenEditFullyCustomModel2Client");
	PB_OpenEditFullyCustomModelHC openEditFullyCustomModelHC;
	openEditFullyCustomModelHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WorldManager *worldMgr = m_root->getWorldMgr();
	if (worldMgr == NULL) return;
	World *pworld = worldMgr->getWorld(openEditFullyCustomModelHC.mapid());
	if (!pworld)
		return;

	if (!FullyCustomModelMgr::GetInstancePtr())
		return;

	ContainerFullyCustomModel *container = dynamic_cast<ContainerFullyCustomModel *>(pworld->getContainerMgr()->getContainer(WCoord(openEditFullyCustomModelHC.containerpos().x(), openEditFullyCustomModelHC.containerpos().y(), openEditFullyCustomModelHC.containerpos().z())));
	FullyCustomModelMgr::GetInstancePtr()->openEditUI2Client(container, openEditFullyCustomModelHC.url().c_str(), openEditFullyCustomModelHC.edited(), openEditFullyCustomModelHC.version(), openEditFullyCustomModelHC.result());
}


void MpGameSurviveNetHandler::handleCloseEditFullyCustomModel2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_CloseFullyCustomModelUIHC closeFullyCustomModelUIHC;
	closeFullyCustomModelUIHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WorldManager *worldMgr = m_root->getWorldMgr();
	if (worldMgr == NULL) return;
	World *pworld = worldMgr->getWorld(closeFullyCustomModelUIHC.mapid());
	if (!pworld)
		return;

	int result = closeFullyCustomModelUIHC.result();
	GetGameEventQue().postCloseEditFullyCustomModel(result);

	if ((result == SAVE_CLOSE_FCM_UI_SUCCESS || result == ONLY_CLOSE_FCM_UI_SUCCESS) && FullyCustomModelMgr::GetInstancePtr())
		FullyCustomModelMgr::GetInstancePtr()->clientSaveEditModelByResult(result);

	ContainerFullyCustomModel *container = dynamic_cast<ContainerFullyCustomModel *>(pworld->getContainerMgr()->getContainer(WCoord(closeFullyCustomModelUIHC.containerpos().x(), closeFullyCustomModelUIHC.containerpos().y(), closeFullyCustomModelUIHC.containerpos().z())));
	if (container && result != CLOSE_FCM_UI_ERR)
		container->updateFullyCustomModelData(closeFullyCustomModelUIHC.skey());
}

void MpGameSurviveNetHandler::handleRespDownLoadRes2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_RespDownLoadResUrlHC respDownLoadResUrlHC;
	respDownLoadResUrlHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (!RoomSyncResMgr::getSingletonPtr())
		return;

	RoomSyncResMgr::getSingletonPtr()->respDownloadUrl(respDownLoadResUrlHC.type(), respDownLoadResUrlHC.externdata(), respDownLoadResUrlHC.downloadurl());
}

void MpGameSurviveNetHandler::handlePreOpenEditFCMUI2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PreOpenEditFCMUIHC preOpenEditFCMUIHC;
	preOpenEditFCMUIHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (!RoomSyncResMgr::getSingletonPtr())
		return;

	int state = preOpenEditFCMUIHC.state();
	GetGameEventQue().postPreOpenEditFCMUI(state);
}

void MpGameSurviveNetHandler::handlePlayerJumpOnce2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerCommonSetHC playerJumpHC;
	playerJumpHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	
	long long objId = playerJumpHC.uin();

	//生物/玩家
	ClientActor *actor = objId2ActorOnClient(objId);
	if (actor)
	{
		if (actor->getObjType() == OBJ_TYPE_ROLE)
		{
			ClientPlayer* player = dynamic_cast<ClientPlayer*>(actor);
			if (player)
			{
				player->jumpOnce();
			}
		}
		else
		{
			actor->jumpOnce();
		}
	}
}



void MpGameSurviveNetHandler::handleCloudServerPlayerPermit2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_CSPlayerPermitHC csPlayerPermitHC;
	csPlayerPermitHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PermitsSubSystem* permitsModule = GET_SUB_SYSTEM(PermitsSubSystem);
	if (permitsModule)
	{
		permitsModule->onCSReset(csPlayerPermitHC);
	}
	//SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_onCSReset",
	///	SandboxContext(nullptr).SetData_Usertype<game::hc::PB_CSPlayerPermitHC>("playerPermitHC", &csPlayerPermitHC));
}

void MpGameSurviveNetHandler::handleCloudServerAuthority2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_CSAuthorityHC csAuthorityHC;
	csAuthorityHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_onCSAuthorityReset",
		SandboxContext(nullptr).SetData_Usertype<game::hc::PB_CSAuthorityHC>("csAuthorityHC", &csAuthorityHC));

}

void MpGameSurviveNetHandler::handleSSTask2Client(const PB_PACKDATA_CLIENT &pkg)
{
//#ifdef _SCRIPT_SUPPORT_
	PB_SSTaskHC taskHC;
	taskHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	
	MINIW::ScriptVM::game()->callFunction("SSTaskReceiveForPlayer", "is", taskHC.taskid(), taskHC.paramjson().c_str());
//#endif
}


void MpGameSurviveNetHandler::handleVehicleAssembleLine2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_VehicleAssembleLineHC vehicleAssembleLineHC;
	vehicleAssembleLineHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	tdr_longlong objid = vehicleAssembleLineHC.objid();

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl && playerCtrl->getWorld() && playerCtrl->getWorld()->getActorMgr())
	{
		ClientActor *actor = objid > 0 ? playerCtrl->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid) : NULL;

		ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
		int from = vehicleAssembleLineHC.from();
		int to = vehicleAssembleLineHC.to();

		WCoord start(from >> 10, (from >> 5) & 0x1f, from & 0x1f);
		WCoord end(to >> 10, (to >> 5) & 0x1f, to & 0x1f);
		if (vehicle)
		{
			vehicle->addBlockLines(start, end);
		}
	}
}

void MpGameSurviveNetHandler::handleVehicleAssembleLineOperate2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_VehicleAssembleLineOperateHC vehicleAssembleLineOperateHC;
	vehicleAssembleLineOperateHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	tdr_longlong objid = vehicleAssembleLineOperateHC.objid();
	bool state = vehicleAssembleLineOperateHC.state();
	WCoord blockpos = WCoord(vehicleAssembleLineOperateHC.blockpos().x(), vehicleAssembleLineOperateHC.blockpos().y(), vehicleAssembleLineOperateHC.blockpos().z());
	if (playerCtrl && playerCtrl->getWorld() && playerCtrl->getWorld()->getActorMgr())
	{
		ClientActor *actor = objid > 0 ? playerCtrl->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid) : NULL;

		ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);

		vehicle->UpdateKeyBindUIState(blockpos,state);
	}
}

void MpGameSurviveNetHandler::handleVehicleBindActor2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_VehicleBindActorHC vehicleAssembleBindHC;
	vehicleAssembleBindHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	long long bindobjid = vehicleAssembleBindHC.bindobjid();
	long long vehicleobjid = vehicleAssembleBindHC.vehicleobjid();
	//bool state = vehicleAssembleBindHC.state();
	WCoord blockpos = WCoord(vehicleAssembleBindHC.blockpos().x(), vehicleAssembleBindHC.blockpos().y(), vehicleAssembleBindHC.blockpos().z());
	ClientActor *actorbind = objId2ActorOnClient(bindobjid);
	if (actorbind)
	{
		auto vehicleComponent = actorbind->getActorBindVehicle();
		if (vehicleComponent)
		{
			vehicleComponent->Bind(vehicleobjid, blockpos, false);
		}
	}
}


/*void MpGameSurviveNetHandler::handleUpdateActionerData2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_UpdateActionerDataHC UpdateActionerDataHC;
	UpdateActionerDataHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	tdr_longlong objid = UpdateActionerDataHC.objid();
	std::string datastr = UpdateActionerDataHC.datastr();
	WCoord blockpos = WCoord(UpdateActionerDataHC.blockpos().x(), UpdateActionerDataHC.blockpos().y(), UpdateActionerDataHC.blockpos().z());
	if (playerCtrl && playerCtrl->getWorld() && playerCtrl->getWorld()->getActorMgr())
	{
		ClientActor *actor = objid > 0 ? playerCtrl->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid) : NULL;

		ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
		if (vehicle&&vehicle->getVehicleWorld())
		{
			VehicleContainerActioner* container = dynamic_cast<VehicleContainerActioner*>(vehicle->getVehicleWorld()->getContainerMgr()->getContainer(blockpos));
			if (container)
				container->updateActionerData(datastr.c_str());
		}
	}
}*/


void MpGameSurviveNetHandler::handleCloudServerChangeState2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_CloudServerChangeHC cloudServerChangeHC;
	cloudServerChangeHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	MINIW::ScriptVM::game()->callFunction("RentShowNoticePopup", "i", cloudServerChangeHC.type());
}

void MpGameSurviveNetHandler::handleUsePackingFCMItem2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_UsePackingFcmItemHC usePackingFcmItemHC;
	usePackingFcmItemHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (PackingFullyCustomModelMgr::GetInstancePtr())
		PackingFullyCustomModelMgr::GetInstancePtr()->handlePackingFcmItemUseResult(usePackingFcmItemHC.result());
}

void MpGameSurviveNetHandler::handleCreatePackingCM2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_CreatePackingCMHC createPackingCMHC;
	createPackingCMHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (MINIW::ScriptVM::game())
		MINIW::ScriptVM::game()->callFunction("CreatePackingCMResultHandle", "i", createPackingCMHC.result());

}

void MpGameSurviveNetHandler::handlePackingFCMData2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PackingFCMDataHC packingFCMDataHC;
	packingFCMDataHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (!FullyCustomModelMgr::GetInstancePtr() || !FullyCustomModelMgr::GetInstancePtr()->getPackingFCMMgr())
		return;

	for (size_t i = 0; i < (size_t)packingFCMDataHC.packingfcms_size(); i++)
	{
		FullyCustomModel *pFcm = ENG_NEW(FullyCustomModel)();

		auto &srcFcmData = packingFCMDataHC.packingfcms(i);
		for (size_t j = 0; j < (size_t)srcFcmData.packingcms_size(); j++)
		{
			auto &srcCmData = srcFcmData.packingcms(j);
			WCoord pos(srcCmData.packingpos().x(), srcCmData.packingpos().y(), srcCmData.packingpos().z());
			Rainbow::Quaternionf quat(srcCmData.quat().x(), srcCmData.quat().y(), srcCmData.quat().z(), srcCmData.quat().w());
			pFcm->setPackingFCMData(pos, srcCmData.name(), srcCmData.model(), quat, srcCmData.dir());
		}

		WCoord minPos(srcFcmData.minpos().x(), srcFcmData.minpos().y(), srcFcmData.minpos().z());
		WCoord maxPos(srcFcmData.maxpos().x(), srcFcmData.maxpos().y(), srcFcmData.maxpos().z());
		pFcm->setPackingFCMInfoByHostSync(srcFcmData.name(), srcFcmData.desc(), srcFcmData.skey(), srcFcmData.dir(), minPos, maxPos, srcFcmData.authuin());

		FullyCustomModelMgr::GetInstancePtr()->getPackingFCMMgr()->addPackingFcmBySync(pFcm);
	}
}

void MpGameSurviveNetHandler::handlePlayerCloudRoomStatusTime2Client(const PB_PACKDATA_CLIENT &pkg)
{

	PB_CloudRoomStatusTimeHC statusTimeHC;
	statusTimeHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	LOG_INFO("handlePlayerCloudRoomStatusTime2Client, status:%d time:%d", statusTimeHC.status(), statusTimeHC.time());

	MINIW::ScriptVM::game()->callFunction("NoticePlayerStatusTime", "ii", statusTimeHC.status(), statusTimeHC.time());
}

void MpGameSurviveNetHandler::handleSensorContainerData2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl && playerCtrl->getWorld() && playerCtrl->getWorld()->getActorMgr() && playerCtrl->getWorld()->getContainerMgr())
	{
		PB_SensorContainerDataHC sensorContainerDataHC;
		sensorContainerDataHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
		WCoord blockPos = MPVEC2WCoord(sensorContainerDataHC.blockpos());
		int sensorValue = sensorContainerDataHC.sensorvalue();
		bool sensorRever = sensorContainerDataHC.isbreverse();
		tdr_longlong objid = sensorContainerDataHC.objid();

		ClientActor *actor = objid > 0 ? playerCtrl->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid) : NULL;

		ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
		WorldContainer* container = NULL;
		if (vehicle&&vehicle->getVehicleWorld())
			container = vehicle->getVehicleWorld()->getContainerMgr()->getContainer(blockPos);
		else
			container = playerCtrl->getWorld()->getContainerMgr()->getContainer(blockPos);

		if (container)
		{
			//红外感应方块
			WorldSensorContainer *curcontainer = dynamic_cast<WorldSensorContainer*>(container);
			if (curcontainer)
			{
				curcontainer->setSensorValue(sensorValue);
			}
			else
			{
				//感应器
				WorldValueSensorContainer *curcontainer = dynamic_cast<WorldValueSensorContainer*>(container);
				if (curcontainer)
				{
					curcontainer->setSensorValueAndIsRever(sensorValue, sensorRever);
				}
			}
		}
	}
}

void MpGameSurviveNetHandler::handleDoorData2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl && playerCtrl->getWorld() && playerCtrl->getWorld()->getActorMgr() && playerCtrl->getWorld()->getContainerMgr())
	{
		PB_DoorDataHC doorDataHC;
		doorDataHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
		WCoord blockPos = MPVEC2WCoord(doorDataHC.blockpos());

		playerCtrl->getWorld()->markBlockForPhyUpdate(blockPos - WCoord(1, 1, 1), blockPos + WCoord(1, 1, 1));
	}
}

void MpGameSurviveNetHandler::handleCarryActor2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerCarryActorHC playerCarryActorHC;
	playerCarryActorHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (!playerCtrl)
	{
		return;
	}
	ClientPlayer *player = dynamic_cast<ClientPlayer *>(objId2ActorOnClient(playerCarryActorHC.playeruin()));
	if (player)
	{
		tdr_longlong objid = playerCarryActorHC.actorid();
		ClientActor *actor = objid > 0 ? playerCtrl->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid) : NULL;

		if (player->hasUIControl())
		{

		}

		player->carryActor(actor);
	}
}

void MpGameSurviveNetHandler::handleVillagerBodyChange2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl && playerCtrl->getWorld() && playerCtrl->getWorld()->getActorMgr())
	{
		PB_VillagerBodyChangeHC bodyChangeHC;
		bodyChangeHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
		auto objid = bodyChangeHC.objid();
		auto changeType = bodyChangeHC.changetype();
		auto changeValue = bodyChangeHC.changevalue();
		auto otherVaule = bodyChangeHC.othervalue();

		ClientActor *actor = objId2ActorOnClient(objid);
		if (actor == NULL)
			return;

		ClientMob *mob = dynamic_cast<ClientMob *>(actor);
		if (changeType == 1 && mob) //设置显示名字
		{
			mob->setDisplayName(otherVaule);
			return;
		}

		ActorVillager *villager = dynamic_cast<ActorVillager *>(actor);
		if (villager)
		{
			auto changeType = bodyChangeHC.changetype();
			auto changeValue = bodyChangeHC.changevalue();
			auto otherVaule = bodyChangeHC.othervalue();
			if (changeType == 2)	//设置发型ID和发型颜色
			{
				int hairId = (int) atoi(otherVaule.c_str());
				villager->setHairColor((unsigned int)changeValue, hairId);
			}
			else if (changeType == 3)	//设置面部ID
			{
				villager->setFaceId((int)changeValue);
			}
			else if (changeType == 4)	//设置随机名字id
			{
				villager->setRandomNameId((int)changeValue);
			}
		}
	}

}

void MpGameSurviveNetHandler::handleTameActor2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerTameActorHC playerTameActorHC;
	playerTameActorHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	tdr_longlong objid = playerTameActorHC.actorid();

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if(playerCtrl && playerCtrl->getWorld())
	{
		ClientActor *actor = objid > 0 ? playerCtrl->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid) : NULL;

		if (actor)
		{
			ClientMob *mob = dynamic_cast<ClientMob *>(actor);
			if (mob)
				mob->setTamedOwnerUin(playerTameActorHC.playeruin());
		}
	}
}

void MpGameSurviveNetHandler::handleVillagerCloth2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_VillagerCloth villagerProfession;
	villagerProfession.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl && playerCtrl->getWorld())
	{
		long long objid = villagerProfession.actorid();
		bool show = villagerProfession.bshow();
		std::string name = villagerProfession.modlename();
		ClientActor * actor = playerCtrl->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid);
		ActorVillager *ager = dynamic_cast<ActorVillager*> (actor);
		if (ager)
		{
			ager->showSkin(name.c_str(), show);
		}
	}
}

void MpGameSurviveNetHandler::handleActorHeadDisplayIcon2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActorHeadDisplayIconHC actorHeadDisplayIconHC;
	actorHeadDisplayIconHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	tdr_longlong objid = actorHeadDisplayIconHC.actorid();
	if (GetWorldManagerPtr())
	{
		ClientActor *actor = objid > 0 ? static_cast<ClientActor*>(GetWorldManagerPtr()->findActorByWID(objid)) : NULL;
		if (actor)
		{
			int itemid = 0;
			int tick = -1;
			if (actorHeadDisplayIconHC.has_itemid())
			{
				itemid = actorHeadDisplayIconHC.itemid();
			}
			if (actorHeadDisplayIconHC.has_tick())
			{
				tick = actorHeadDisplayIconHC.tick();
			}
			actor->getBody()->setHeadDisplayIcon(itemid, tick);
		}
	}
}

void MpGameSurviveNetHandler::handleActorPlayAnimById2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActorPlayAnimByIdHC actorPlayAnimByIdHC;
	actorPlayAnimByIdHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	tdr_longlong objid = actorPlayAnimByIdHC.actorid();
	int loopmode = -1;
	if (actorPlayAnimByIdHC.has_loopmode())
	{
		loopmode = actorPlayAnimByIdHC.loopmode();
	}
	int layer = -1;
	if (actorPlayAnimByIdHC.has_layer())
	{
		loopmode = actorPlayAnimByIdHC.layer();
	}
	if(playerCtrl != NULL && playerCtrl->getWorld() != NULL )
	{
		ClientActor *actor = objid > 0 ? playerCtrl->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid) : NULL;
		if (actor)
		{
			actor->playAnimById(actorPlayAnimByIdHC.animid(), loopmode, layer);
		}
	}
}

void MpGameSurviveNetHandler::handleActorPlayAnimByIdNew2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_ActorPlayAnimByIdHC actorPlayAnimByIdHC;
	actorPlayAnimByIdHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl* playerCtrl = m_root->getPlayerControl();
	tdr_longlong objid = actorPlayAnimByIdHC.actorid();
	int loopmode = -1;
	if (actorPlayAnimByIdHC.has_loopmode())
	{
		loopmode = actorPlayAnimByIdHC.loopmode();
	}
	int layer = -1;
	if (actorPlayAnimByIdHC.has_layer())
	{
		loopmode = actorPlayAnimByIdHC.layer();
	}
	float crossfade = -1.0f;
	if (actorPlayAnimByIdHC.has_crossfade())
	{
		crossfade = actorPlayAnimByIdHC.crossfade();
	}
	if (playerCtrl != NULL && playerCtrl->getWorld() != NULL)
	{
		ClientActor* actor = objid > 0 ? playerCtrl->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid) : NULL;
		if (actor)
		{
			actor->playAnimByIdNew(actorPlayAnimByIdHC.animid(), loopmode, layer, crossfade);
		}
	}
}

void MpGameSurviveNetHandler::handlePlayerDownedStateChange2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL) return;
	if (playerCtrl->getWorld() == NULL) return;

	PB_PlayerDownedStateChangeHC stateChangeMsg;
	stateChangeMsg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	// 找到相应的玩家对象
	ClientPlayer* player = uin2Player(stateChangeMsg.playerid());
	if (player == NULL)
		return;

	// 找到玩家的倒地状态属性
	PlayerAttrib* playerAttrib = dynamic_cast<PlayerAttrib*>(playerCtrl->getPlayerAttrib());
	if (!playerAttrib)
		return;

	PlayerDownedStateAttrib* downedStateAttrib = playerAttrib->getDownedStateAttrib();
	if (!downedStateAttrib)
		return;

	LOG_WARNING("DownedState ReviveSuccess = %f", stateChangeMsg.revive_success_chance());

	downedStateAttrib->setReviveSuccessChance(stateChangeMsg.revive_success_chance());
	downedStateAttrib->setTotalDownedTicks(stateChangeMsg.total_downed_ticks());
	downedStateAttrib->setDownedHealthDecayPerTick(stateChangeMsg.downed_health_decay_per_tick());

	// 根据消息更新玩家的倒地状态
	int newStateType = stateChangeMsg.newstatetype();
	downedStateAttrib->setDownedHealth(stateChangeMsg.downedhealth());//, stateChangeMsg.maxdownedhealth());

	// 如果是被救援状态，设置救援者ID
	if (stateChangeMsg.has_reviveractorid() && newStateType == 2) // 2=被救援状态
	{
		downedStateAttrib->setReviverActorId(stateChangeMsg.reviveractorid());
	}

	// 根据状态类型切换状态
	switch (newStateType)
	{
	case 0: // 正常状态
		downedStateAttrib->changeToNormalState();
		break;
	case 1: // 倒地状态
		downedStateAttrib->changeToDownedState();
		break;
	case 2: // 被救援状态
		downedStateAttrib->changeToBeingRevivedState();
		break;
	default:
		break;
	}

	// 如果有UI需要更新，可以在这里触发UI更新
	if (MINIW::ScriptVM::game())
	{
		MINIW::ScriptVM::game()->callFunction("OnPlayerDownedStateChanged", "iii",
			(int)stateChangeMsg.playerid(),
			stateChangeMsg.oldstatetype(),
			stateChangeMsg.newstatetype());
	}
}

void MpGameSurviveNetHandler::handlePlayerDownedHealthUpdate2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL) return;
	if (playerCtrl->getWorld() == NULL) return;

	PB_PlayerDownedHealthUpdateHC healthUpdateMsg;
	healthUpdateMsg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	// 找到相应的玩家对象
	ClientPlayer* player = uin2Player(healthUpdateMsg.playerid());
	if (player == NULL)
		return;

	// 找到玩家的倒地状态属性
	PlayerAttrib* playerAttrib = dynamic_cast<PlayerAttrib*>(player->getPlayerAttrib());
	if (!playerAttrib)
		return;

	PlayerDownedStateAttrib* downedStateAttrib = playerAttrib->getDownedStateAttrib();
	if (!downedStateAttrib)
		return;

	// 只有在倒地状态或被救援状态时才更新血量
	int currentState = healthUpdateMsg.currentstate();
	if (currentState == 1 || currentState == 2) // 1=倒地状态, 2=被救援状态
	{
		// 更新倒地血量
		downedStateAttrib->setDownedHealth(healthUpdateMsg.downedhealth());
		
		// 如果是被救援状态，更新救援进度和救援者ID
		if (currentState == 2)
		{
			if (healthUpdateMsg.has_reviveprogress())
				downedStateAttrib->setReviveProgress(healthUpdateMsg.reviveprogress());
			
			if (healthUpdateMsg.has_reviveractorid())
				downedStateAttrib->setReviverActorId(healthUpdateMsg.reviveractorid());
		}

		//// 触发UI更新（Lua回调）
		//if (MINIW::ScriptVM::game())
		//{
		//	MINIW::ScriptVM::game()->callFunction("OnPlayerDownedHealthChanged", "iff",
		//		(int)healthUpdateMsg.playerid(),
		//		healthUpdateMsg.downedhealth(),
		//		healthUpdateMsg.maxdownedhealth());
		//}
	}
}

void MpGameSurviveNetHandler::handleVillageTotemTip2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_VillageTotemTipHC villageTotemDataHC;
	villageTotemDataHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (!playerCtrl)
	{
		return;
	}
	World * pworld = playerCtrl->getWorld();
	if (pworld)
	{
		int nvillager = villageTotemDataHC.villagernum();
		WCoord bottomcenterpos(villageTotemDataHC.blockpos().x(), villageTotemDataHC.blockpos().y(), villageTotemDataHC.blockpos().z());
		char info[128];
		sprintf(info, "%s%d", GetDefManagerProxy()->getStringDef(35121), nvillager);
		pworld->getEffectMgr()->GetGameEffectMgr()->playText3DEffect(info, bottomcenterpos, 40);
	}
}

void MpGameSurviveNetHandler::handleVillageTotemActive2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_VillageTotemActiveHC villageTotemActiveHC;
	villageTotemActiveHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (GetWorldManagerPtr())
	{
		int uin = villageTotemActiveHC.uin();
		WCoord pos = MPVEC2WCoord(villageTotemActiveHC.blockpos());
		GetWorldManagerPtr()->getWorldInfoManager()->setVillagePoint(uin, pos);
		GetWorldManagerPtr()->getWorldInfoManager()->removeAllVillageTotem(uin);
		GetWorldManagerPtr()->getWorldInfoManager()->addNewVillageTotem(uin, pos);
	}
}

void MpGameSurviveNetHandler::handleImportModel2Client(const PB_PACKDATA_CLIENT &pkg)
{
	if (ImportCustomModelMgr::GetInstancePtr())
	{
		PB_ImportModelHC importModelHC;
		importModelHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
		RepeatedPtrField<PB_ImportModelData> pDatas = importModelHC.models();
		for (size_t i=0; i< (unsigned int)pDatas.size(); ++i)
		{
			const PB_ImportModelData &model = pDatas.Get(i);
			ImportCustomModel *pModel = ENG_NEW(ImportCustomModel)();
			pModel->initWithData(model.key(),
				model.name(),
				model.desc(),
				model.type(),
				model.authuin(),
				model.authname(),
				model.filename()
			);
			if (!ImportCustomModelMgr::GetInstancePtr()->addMapImportModels(pModel))
			{
				OGRE_DELETE(pModel);
			}
		}
	}
}

void MpGameSurviveNetHandler::handlePlayerArch2Client(const PB_PACKDATA_CLIENT &pkg)
{
	GetClientInfoProxy()->handlePlayerArch2Client(this, pkg);
}

void MpGameSurviveNetHandler::handleLightning2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_LightningHC lightningHC;
	lightningHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (g_pPlayerCtrl && g_pPlayerCtrl->getWorld() && g_pPlayerCtrl->getWorld()->m_Environ)
	{
		WCoord tartetPos(lightningHC.targetpos().x(), lightningHC.targetpos().y(), lightningHC.targetpos().z());
		g_pPlayerCtrl->getWorld()->m_Environ->createLightning(tartetPos);
	}
}

void MpGameSurviveNetHandler::handleInteractMobPack2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_InteractMobPackHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (g_pPlayerCtrl && g_pPlayerCtrl->getWorld())
	{
		g_pPlayerCtrl->InteractMobPack(msg.uiname(), msg.param(), msg.mobid());
	}
}

void MpGameSurviveNetHandler::handleUpdateMobBackpack2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_UpdateMobBackpackHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl && playerCtrl->getWorld())
	{
		ClientMob *mob = playerCtrl->getWorld()->getActorMgr()->ToCastMgr()->findMobByWID(msg.mobid());
		if (!mob|| !mob->getBags())
			return;

		for (int i = 0; i < msg.iteminfo_size(); i++)
		{
			const PB_ItemData& data = msg.iteminfo(i);
			BackPackGrid* grid = mob->getBags()->index2Grid(data.index());
			restoreGridData(grid, data);
		}
		GetGameEventQue().postBackpackChange(TAMED_MOB_START_INDEX);
	}
}

void MpGameSurviveNetHandler::handleSFActivityClient(const PB_PACKDATA_CLIENT &pkg)
{
	PB_SFActivity_HC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl)
	{
		playerCtrl->addSFActivity(msg.type(), msg.taskid(), msg.value(), false);
	}
}

void MpGameSurviveNetHandler::handleOpenDevGoodsBuyDialogClient(const PB_PACKDATA_CLIENT& pkg)
{
	PB_OpenDevGoodsBuyDialogHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl)
	{
		playerCtrl->openDevGoodsBuyDialog(msg.itemid(), msg.desc().c_str());
	}
}

void MpGameSurviveNetHandler::handleChangeGraphics2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL || playerCtrl->getWorld() == NULL) return;
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("GraphicsMgr_handleChangeGraphics2Client", SandboxContext(nullptr).SetData_Userdata("PB_PACKDATA_CLIENT", "pkg", (void*)&pkg));
}

void MpGameSurviveNetHandler::handleShapeAdditionAnim2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_ShapeAdditionAnimHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	bool status = msg.status();
	int uin = msg.uin();
	ClientPlayer *player = uin2Player(uin);
	if (player)
	{
		if(status)
			player->showSpecailTryShapeAnim();
		else
			player->hideSpecailTryShapeAnim();
	}
}

void MpGameSurviveNetHandler::handleGodTempleCreate2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_GodTempleCreateHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	bool onoff = msg.onoff();
	int worldid = msg.worldid();

	if (GetWorldManagerPtr())
	{
		World* pWorld = GetWorldManagerPtr()->getWorld(worldid);
		if (pWorld)
		{
			pWorld->SetGodTempleCreateActive(onoff);
		}
	}
}


void MpGameSurviveNetHandler::handleVoiceInform2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_VoiceInformHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientPlayer *player = uin2Player(msg.uin());
	if (player == NULL)
	{
		return;
	}
	SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("PB_VOICE_INFORM_HC_2", SandboxContext(nullptr).SetData_Number("player_uin", msg.uin()).
			SetData_UserObject<PB_VoiceInformHC>("msg", msg)
		);
}

void MpGameSurviveNetHandler::handleCustomBaseModel2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_CustomBaseModelHC baseModelHC;
	baseModelHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (GetWorldManagerPtr() && GetWorldManagerPtr()->getBaseSettingManager())
	{
		int teamid = baseModelHC.teamid();
		const std::string& data = baseModelHC.modeldata();
		GetWorldManagerPtr()->getBaseSettingManager()->setPlayerBaseModelData(data.c_str(), teamid);

		int uin = baseModelHC.uin();
		ClientPlayer *player = uin2Player(uin);
		if (player != NULL) player->setBaseModel(player->getTeam());
	}
}

void MpGameSurviveNetHandler::handleChangeActorModel2Client(const PB_PACKDATA_CLIENT& pkg)
{
	if (!g_WorldMgr)
		return;
	PB_ChangeActorModelHC msg;
	if (msg.ParseFromArray(pkg.MsgData, pkg.ByteSize))
	{
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("ChangeModelMgr_handleChangeActorModel2Client", SandboxContext(NULL)
			.SetData_Userdata("PB_PACKDATA_CLIENT", "pkg", (void*)&msg));
	}

}

void MpGameSurviveNetHandler::handleNotifiyAvtarModel2Client(const PB_PACKDATA_CLIENT& pkg)
{
	if (!g_WorldMgr)
		return;

	PB_NotifiyActorModelHC msg;
	if (msg.ParseFromArray(pkg.MsgData, pkg.ByteSize))
	{
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("ChangeModelMgr_handleNotifiyAvtarModel2Client", SandboxContext(nullptr).SetData_UserObject<PB_NotifiyActorModelHC>("msg", msg));
	}
}


void MpGameSurviveNetHandler::handleNotifyStarStationAdded2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_AddStarStationDef msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	PB_StarStationDef starStationDef = msg.starstationdef();
	GetStarStationTransferMgr().addStarStationDef(starStationDef.mapid(), MPVEC2WCoord(starStationDef.consolepos()),starStationDef.isconsoleactive(),starStationDef.isconsolesign(),starStationDef.starstationid(),starStationDef.starstationname(), false, starStationDef.stationtype(), starStationDef.stationextradata());
}

void MpGameSurviveNetHandler::handleNotifyStarStationRemoved2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_DelStarStationDef msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (!GetWorldManagerPtr())
		return;

	std::vector<WorldStarStationTransferCabinContainer*> vecActiveTransferCabinContainer;
	StarStationTransferDef *starStationDef = GetStarStationTransferMgr().getStarStationDef(msg.srcstarstationid());
	if(starStationDef)
	{
		int nCount = starStationDef->getCabinCount();
		for(int i = 0; i < nCount; ++i)
		{
			StarStationCabinDef *cabinDef = starStationDef->getCabinDefByIndex(i);
			if(cabinDef && cabinDef->bindPlayerUin > 0)
			{
				ClientPlayer *player = static_cast<ClientPlayer*>(GetWorldManagerPtr()->getPlayerByUin(cabinDef->bindPlayerUin));
				if(player)
				{
					if(player == g_pPlayerCtrl)
					{
						ScriptVM::game()->callFunction("OnStarStationConsoleRemoved", "i", msg.srcstarstationid());
					}
				}

				World *pWorld = GetWorldManagerPtr()->getWorld(starStationDef->mapID);
				if(pWorld)
				{
					WorldStarStationTransferCabinContainer *container = dynamic_cast<WorldStarStationTransferCabinContainer*>(pWorld->getContainerMgr()->getContainer(cabinDef->cabinPosX, cabinDef->cabinPosY, cabinDef->cabinPosZ));
					if(container)
					{
						container->setStatus(STARSTATION_INACTIVE);
						container->connectTo(-1);
						vecActiveTransferCabinContainer.push_back(container);
					}
				}
			}
		}
	}
	
	GetStarStationTransferMgr().delStarStationDef(msg.srcstarstationid(), MPVEC2WCoord(msg.cabinpos()), false);

	for(unsigned int i = 0; i < vecActiveTransferCabinContainer.size(); ++i)
	{
		WorldStarStationTransferCabinContainer *container = vecActiveTransferCabinContainer[i];
		World *pWorld = GetWorldManagerPtr()->getWorld(container->getMapid());
		if(pWorld)
		{
			container->findNeighborTransferConsoleBlockAndUpdateStatus(pWorld, false);
		}
		
		int bindPlayerUin = container->getBindPlayerUin();
		if(bindPlayerUin > 0)
		{
			ClientPlayer *player = static_cast<ClientPlayer*>(GetWorldManagerPtr()->getPlayerByUin(bindPlayerUin));
			if(player)
			{
				if(player == g_pPlayerCtrl)
				{
					WCoord cabinPos = container->getTransferPosition();
					MINIW::ScriptVM::game()->callFunction("OnPlayerEnterStarStationCabin", "iiiii", STARSTATION_ACTIVATED, container->getConnectedStarStationID(), cabinPos.x, cabinPos.y, cabinPos.z);
				}
			}
		}
	}

}


void MpGameSurviveNetHandler::handleNotifyStarStationChangeNameStatus2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_ChangeStarStationNameStatus msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	GetStarStationTransferMgr().updateStarStationDef(msg.starstationid(),msg.starstationname(),msg.isactive(), msg.issign(), NULL, false);
}

void MpGameSurviveNetHandler::handleNotifyEnterStarStationCabin2Client(const PB_PACKDATA_CLIENT& pkg) 
{
	PB_EnterStarStationCabin msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	int starStationId = msg.starstationid();
	int cabinPosX = msg.cabinpos().x();
	int cabinPosY = msg.cabinpos().y();
	int cabinPosZ = msg.cabinpos().z();
	int playerUin = msg.uin();
	int cabinStatus = msg.status();
	bool result = msg.result();
	
	if (!GetWorldManagerPtr())
		return;

	if(result)
	{
		StarStationTransferDef* starStationDef = GetStarStationTransferMgr().getStarStationDef(starStationId);
		if(starStationDef)
		{
			starStationDef->updateCabinStatus(cabinPosX, cabinPosY, cabinPosZ, cabinStatus);
			starStationDef->updateCabinBindPlayerUin(cabinPosX, cabinPosY, cabinPosZ, playerUin);
		}

		ClientPlayer *player = static_cast<ClientPlayer*>(GetWorldManagerPtr()->getPlayerByUin(playerUin));
		if(player)
		{
			player->getLocoMotion()->m_yOffset = 0;

			int offY = BLOCK_SIZE/2;
			int offX = 0;
			int offZ = 0;
			World *pWorld = player->getWorld();
			if(pWorld)
			{
				WCoord cabinPos(cabinPosX, cabinPosY, cabinPosZ);
				WorldStarStationTransferCabinContainer *container = dynamic_cast<WorldStarStationTransferCabinContainer *>(pWorld->getContainerMgr()->getContainer(cabinPos));
				if(container)
				{
					container->setBindPlayerUin(playerUin);
				}

				int blockData = pWorld->getBlockData(cabinPos);
				int placeDir = blockData & 3;
				float playerYaw = 180.0f;
				if (placeDir == DIR_NEG_X)
				{
					offX = BLOCK_SIZE;
					playerYaw = 90.0f;
				}
				else if (placeDir == DIR_POS_X)
				{
					offZ = BLOCK_SIZE;
					playerYaw = -90.0f;
				}
				else if (placeDir == DIR_NEG_Z)
				{
					offX = BLOCK_SIZE;
					offZ = BLOCK_SIZE;
					playerYaw = 0.0f;
				}

				WCoord playerpos = cabinPos * BLOCK_SIZE + WCoord(offX, offY, offZ);
				player->getLocoMotion()->setRotateRaw(playerYaw);
				player->getLocoMotion()->setRotatePitch(-6.0f);
				player->getLocoMotion()->setPosition(playerpos.x, playerpos.y, playerpos.z);
				player->getLocoMotion()->m_Motion = Rainbow::Vector3f(0, 0, 0);
			}

			if(player == g_pPlayerCtrl)
			{
				player->toActionBodyState("ToSit");
				GetGameEventQue().postRidingChange(1);
				ScriptVM::game()->callFunction("OnPlayerEnterStarStationCabin", "iiiii", cabinStatus, starStationId, cabinPosX, cabinPosY, cabinPosZ);
			}
			
			player->setSitting(true);
			player->getBody()->setIsInStarStationCabin(true);
		}
	}
	else
	{
		GetLuaInterfaceProxy().showGameTips(80039, 3, true);
	}
}

void MpGameSurviveNetHandler::handleNotifyLeaveStarStationCabin2Client(const PB_PACKDATA_CLIENT& pkg) 
{
	if (!GetWorldManagerPtr())
		return;

	PB_LeaveStarStationCabin msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	int starStationId = msg.starstationid();
	int cabinPosX = msg.cabinpos().x();
	int cabinPosY = msg.cabinpos().y();
	int cabinPosZ = msg.cabinpos().z();
	int playerUin = msg.uin();
	int cabinStatus = msg.status();

	ClientPlayer *player = NULL;
	if (GetWorldManagerPtr()) {
		player = static_cast<ClientPlayer*>(GetWorldManagerPtr()->getPlayerByUin(playerUin));
	}
	if(player)
	{
		WorldStarStationTransferCabinContainer *container = dynamic_cast<WorldStarStationTransferCabinContainer *>(player->getWorld()->getContainerMgr()->getContainer(cabinPosX, cabinPosY, cabinPosZ));
		if(container)
		{
			container->setBindPlayerUin(0);
			container->setStatus((StarStationTransferStatus)cabinStatus, false);
		}

		player->setSitting(false);
		player->getBody()->setIsInStarStationCabin(false);
	}
}

void MpGameSurviveNetHandler::handleNotifyUpgradeStarStationCabin2Client(const PB_PACKDATA_CLIENT& pkg) 
{
	if (!GetWorldManagerPtr())
		return;

	PB_UpgradeStarStationCabinHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	IClientPlayer* iplayer = GetWorldManagerPtr()->getPlayerByUin(msg.playeruin());
	if(iplayer)
	{
		ClientPlayer* player = iplayer->GetPlayer();
		if(msg.result())
		{
			if(g_pPlayerCtrl == player)
			{
				GetLuaInterfaceProxy().showGameTips(80035, 3, true);
			}

			WorldStarStationTransferCabinContainer *container = dynamic_cast<WorldStarStationTransferCabinContainer *>(player->getWorld()->getContainerMgr()->getContainer(MPVEC2WCoord(msg.cabinpos())));
			if(container)
			{
				container->upgrade();
			}

			player->addAchievement(1, ACHIEVEMENT_USEITEM, ITEM_INTELLIGENT_UNIT, 1);
			player->updateTaskSysProcess(TASKSYS_USE_ITEM, ITEM_INTELLIGENT_UNIT, 0, 1);
		}
		else
		{
			GetLuaInterfaceProxy().showGameTips(80039, 3, true);
		}
	}
}

void MpGameSurviveNetHandler::handleNotifyUpdateStarStationCabinStatus2Client(const PB_PACKDATA_CLIENT& pkg) 
{
	PB_UpdateStarStationCabinStatus msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	GetStarStationTransferMgr().updateStarStationCabinStatus(msg.starstationid(), MPVEC2WCoord(msg.cabinpos()), msg.status(), false);
}

void MpGameSurviveNetHandler::handleNotifyUpdateStarStationStatusEnd2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_UpdateStarStationCabinStatus msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	GetStarStationTransferMgr().updateStarStationCabinStatusEnd(msg.starstationid(), msg.status(), false);
}

void MpGameSurviveNetHandler::handleNotifyUpdateStarStationCabinAdded2Client(const PB_PACKDATA_CLIENT& pkg) 
{
	PB_UpdateStarStationCabinAdded msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	PB_StarStationCabinDef pbCabinDef = msg.cabindef();
	StarStationCabinDef cabinDef;
	cabinDef.cabinPosX = pbCabinDef.cabinpos().x();
	cabinDef.cabinPosY = pbCabinDef.cabinpos().y();
	cabinDef.cabinPosZ = pbCabinDef.cabinpos().z();
	cabinDef.cabinStatus = pbCabinDef.cabinstatus();
	cabinDef.bindPlayerUin = pbCabinDef.bindplayeruin();
	GetStarStationTransferMgr().updateStarStationCabinAdded(msg.starstationid(), cabinDef, false);
	if(cabinDef.bindPlayerUin > 0 && GetWorldManagerPtr())
	{
		IClientPlayer* iplayer = GetWorldManagerPtr()->getPlayerByUin(cabinDef.bindPlayerUin);
		if(iplayer)
		{
			ClientPlayer* player = iplayer->GetPlayer();
			if(player == g_pPlayerCtrl)
			{
				MINIW::ScriptVM::game()->callFunction("OnPlayerEnterStarStationCabin", "iiiii", cabinDef.cabinStatus, msg.starstationid(), cabinDef.cabinPosX, cabinDef.cabinPosY, cabinDef.cabinPosZ);
			}
		}
	}
}

void MpGameSurviveNetHandler::handleNotifyUpdateStarStationCabinRemoved2Client(const PB_PACKDATA_CLIENT& pkg) 
{
	PB_UpdateStarStationCabinRemoved msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int bindPlayerUin = -1;
	int mapID = -1;
	StarStationTransferDef *starStationDef = GetStarStationTransferMgr().getStarStationDef(msg.starstationid());
	if(starStationDef)
	{
		StarStationCabinDef cabinDef;
		if(starStationDef->hasCabin(msg.cabinpos().x(), msg.cabinpos().y(), msg.cabinpos().z(), cabinDef))
		{
			bindPlayerUin = cabinDef.bindPlayerUin;
		}

		mapID = starStationDef->mapID;
	}
	else
	{
		if(msg.has_mapid())
		{
			mapID = msg.mapid();
			if(GetWorldManagerPtr())
			{
				World *pWorld = GetWorldManagerPtr()->getWorld(mapID);
				if(pWorld)
				{
					WorldStarStationTransferCabinContainer *container = dynamic_cast<WorldStarStationTransferCabinContainer*>(pWorld->getContainerMgr()->getContainer(MPVEC2WCoord(msg.cabinpos())));
					if(container)
					{
						bindPlayerUin = container->getBindPlayerUin();
						container->setBindPlayerUin(0);
						container->setStatus(STARSTATION_INACTIVE);
						container->connectTo(-1);
					}
				}
			}
		}
	}

	if(bindPlayerUin > 0 && GetWorldManagerPtr())
	{
		IClientPlayer* iplayer = GetWorldManagerPtr()->getPlayerByUin(bindPlayerUin);
		if(iplayer)
		{
			ClientPlayer* player = iplayer->GetPlayer();
			player->setSitting(false);
			player->getBody()->setIsInStarStationCabin(false);

			if(player == g_pPlayerCtrl)
			{
				GetGameEventQue().postRidingChange(0);
				ScriptVM::game()->callFunction("OnStarStationCabinRemoved", "iiii", mapID, msg.cabinpos().x(), msg.cabinpos().y(), msg.cabinpos().z());
			}
		}
	}

	GetStarStationTransferMgr().updateStarStationCabinRemoved(msg.starstationid(),MPVEC2WCoord(msg.cabinpos()), mapID, false);
}

void MpGameSurviveNetHandler::handleNotifyAddUnfinishedTransferRecord2Client(const PB_PACKDATA_CLIENT& pkg) 
{
	PB_AddUnfinishedTransferRecord msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	PB_UnfinishedStarStationTransferRecord pbUnfinishedTransferRecord = msg.unfinishedtransferrecord();
	UnfinishedStarStationTransferRecord unfinishedTransferRecord;
	unfinishedTransferRecord.destStarStationID = pbUnfinishedTransferRecord.deststarstationid();
	unfinishedTransferRecord.destMapID = pbUnfinishedTransferRecord.destmapid();
	unfinishedTransferRecord.srcCabinPosX = pbUnfinishedTransferRecord.srccabinpos().x();
	unfinishedTransferRecord.srcCabinPosY = pbUnfinishedTransferRecord.srccabinpos().y();
	unfinishedTransferRecord.srcCabinPosZ = pbUnfinishedTransferRecord.srccabinpos().z();
	unfinishedTransferRecord.status = pbUnfinishedTransferRecord.cabinstatus();
	GetStarStationTransferMgr().addUnfinishedTransferRecord(msg.srcstarstationid(), unfinishedTransferRecord, false);
}

void MpGameSurviveNetHandler::handleNotifyUpdateUnfinishedTransferRecordStatus2Client(const PB_PACKDATA_CLIENT& pkg) 
{
	PB_UpdateUnfinishedTransferRecordStatus msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	GetStarStationTransferMgr().updateUnfinishedTransferRecordStatus(msg.starstationid(), MPVEC2WCoord(msg.cabinpos()), false);
}

void MpGameSurviveNetHandler::handleNotifyRemoveUnfinishedTransferRecord2Client(const PB_PACKDATA_CLIENT& pkg) 
{
	PB_RemoveUnfinishedTransferRecord msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	GetStarStationTransferMgr().removeUnfinishedTransferRecord(msg.starstationid(),MPVEC2WCoord(msg.cabinpos()), false);
}

void MpGameSurviveNetHandler::handleStarStationData2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_StarStationDataHC starStationDataHC;
	starStationDataHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	GetStarStationTransferMgr().loadStarStationDataByHost(starStationDataHC);
}

void MpGameSurviveNetHandler::handleNotifyStarStationTransfer2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerTransferByStarStationHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	int nPlayerUin = msg.uin();
	if(GetWorldManagerPtr() != NULL)
	{
		IClientPlayer* iplayer = GetWorldManagerPtr()->getPlayerByUin(nPlayerUin);
		if(iplayer && iplayer->GetPlayer() == g_pPlayerCtrl)
		{
			ScriptVM::game()->callFunction("OnStarStationTeleportSuccess", "i", msg.destmapid());
		}
	}
}

void MpGameSurviveNetHandler::handleNotifyPlayerTransfer2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_PlayerTransferHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	GetStarStationTransferMgr().transferPlayerByClient(msg);
}

void MpGameSurviveNetHandler::handleNotifyModBlockChangeColor2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_ModBlockColorAnimHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (GetWorldManagerPtr() != NULL && g_pPlayerCtrl)
	{
		if (g_pPlayerCtrl->getCurMapID() == msg.mapid())
		{
			WCoord tmp(msg.targetpos().x(), msg.targetpos().y(), msg.targetpos().z());
			g_ModPackMgr->receiveModBlockChangeColor(tmp, msg.interval(), msg.color(), msg.has_changetimes(), msg.has_changetimes() ? msg.changetimes() : 0, GetWorldManagerPtr()->getWorld(g_pPlayerCtrl->getCurMapID()));
			//GetWorldManagerPtr()->getWorld(g_pPlayerCtrl->getCurMapID())->receiveModBlockChangeColor(tmp, msg.interval(), msg.color(), msg.has_changetimes(), msg.has_changetimes() ? msg.changetimes() : 0);
		}
	}
}

void MpGameSurviveNetHandler::handleNotifyActivateStarStation2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ActivateStarStationHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if(msg.result())
	{
		if(GetWorldManagerPtr())
		{
			World *pWorld = GetWorldManagerPtr()->getWorld(msg.mapid());
			if(pWorld)
			{
				WorldStarStationTransferConsoleContainer* container = dynamic_cast<WorldStarStationTransferConsoleContainer*>(pWorld->getContainerMgr()->getContainer(MPVEC2WCoord(msg.consolepos())));
				if (container)
				{
					ClientPlayer *player = NULL;
					if(msg.has_playeruin())
					{
						IClientPlayer* iplayer = GetWorldManagerPtr()->getPlayerByUin(msg.playeruin());
						player = iplayer ? iplayer->GetPlayer() : nullptr;
					}
					container->setActive(player, false);
					container->setSign(true, false);
					container->setStarStationId(msg.starstationid());
					container->setStarStationName(msg.starstationname());
				}
			}
		}

		GetStarStationTransferMgr().updateStarStationDef(msg.starstationid(), msg.starstationname(), true, true, NULL, false);
	}
}


void MpGameSurviveNetHandler::handleNotifyUpdateStarStationSignInfo2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_UpdateStarStationSignInfoHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	StarStationTransferDef *starStationDef = GetStarStationTransferMgr().getStarStationDef(msg.starstationid());
	if(starStationDef && GetWorldManagerPtr())
	{
		World *pWorld = GetWorldManagerPtr()->getWorld(starStationDef->mapID);
		if(pWorld)
		{
			WorldStarStationTransferConsoleContainer* container = dynamic_cast<WorldStarStationTransferConsoleContainer*>(pWorld->getContainerMgr()->getContainer(starStationDef->consolePosX, starStationDef->consolePosY, starStationDef->consolePosZ));
			if (container)
			{
				container->setSign(msg.issign(), false);
			}
		}
	}
}

void MpGameSurviveNetHandler::handleNotifyStarStationResult2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_StarStationTransferDeductFeeHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	MINIW::ScriptVM::game()->callFunction("OnStarStationTransferResult", "ii", msg.transfertype(), msg.result());
}

//复活点相关
void MpGameSurviveNetHandler::handlePlayerRevivePoint2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_PlayerRevivePointHC PlayerRevivePointHC;
	PlayerRevivePointHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(PlayerRevivePointHC.uin());
	if (player == NULL)
	{
		return;
	}

	WCoord revivepoint = MPVEC2WCoord(PlayerRevivePointHC.revivepoint());
	WCoord spawnpoint = PlayerRevivePointHC.has_spawnpoint() ? MPVEC2WCoord(PlayerRevivePointHC.spawnpoint()) : WCoord(0, -1, 0);
	
	SandboxContext context;
	context.SetData_UserObject("spawn", spawnpoint);
	context.SetData_UserObject("revive", revivepoint);
	context.SetData_Number("mapid", PlayerRevivePointHC.mapid());
	player->Event().Emit("revive_revivePoint2Client", context);
	//player->setAccountWorldPoint(PlayerRevivePointHC.mapid(), spawnpoint, revivepoint);
	//player->playClientReviveEffect(PlayerRevivePointHC.mapid(), revivepoint);

	if (GetWorldManagerPtr() && player->getWorld() && (player->getWorld()->getCurMapID() == PlayerRevivePointHC.mapid()))
	{
		GetWorldManagerPtr()->setRevivePointEx(revivepoint, player->getWorld());
		GetWorldManagerPtr()->setSpawnPointEx(spawnpoint, player->getWorld());
	}
}
// 播放奥特曼背景音乐
void MpGameSurviveNetHandler::handlePlayAltmanMusic2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayAltmanMusicHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	GetGameEventQue().postPlayAltmanMusic(msg.blockid());

	if (g_pPlayerCtrl)
	{
		// 播放音效
		string effectName = "";
		switch (msg.blockid())
		{
		case 470:
			effectName = "buff.shodai_chua";
			break;
		case 471:
			effectName = "buff.diga_chua";
			break;
		case 472:
			effectName = "buff.daina_chua";
			break;
		case 473:
			effectName = "buff.gaia_chua";
			break;
		case 474:
			effectName = "buff.zero_chua";
			break;
		case 475:
			effectName = "buff.ginga_chua";
			break;
		}
		auto soundComp = g_pPlayerCtrl->getSoundComponent();
		if (soundComp)
		{
			soundComp->playSoundFollowActor(effectName.c_str(), 1.0f, 1.0f, false);
		}
	}
}

void MpGameSurviveNetHandler::handleAchievementUpdate2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_AchievementSyncHC msg;
	// //2021/07/14 参数填错 codeby:wudeshen
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	MNSandbox::GetGlobalEvent().Emit<game::hc::PB_AchievementSyncHC&>("AchievementManager_handleAchievementUpdate2Client", msg);
}

void MpGameSurviveNetHandler::handleAchievementInit2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_AchievementSyncHC msg;
	// //2021/07/14 参数填错 codeby:wudeshen
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	MNSandbox::GetGlobalEvent().Emit<game::hc::PB_AchievementSyncHC &>("AchievementManager_handleAchievementInit2Client", msg);
}

void MpGameSurviveNetHandler::handleNotifyUpdateToolModelTexture2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL || !player->getWorld() || !player->getWorld()->getActorMgr())
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_NotifyUpdateToolModelTextureHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (player)
	{
		player->updateToolModelTexture(msg.textureindex());
	}

}


void MpGameSurviveNetHandler::handleNotifyUpdateToolModelTexture2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL) 
		return;

	PB_NotifyUpdateToolModelTextureHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int uin = msg.uin();
	ClientPlayer * player = uin2Player(msg.uin());
	if (player)
	{
		player->updateToolModelTexture(msg.textureindex(), false);
	}

}

void MpGameSurviveNetHandler::handleNewAdNpcAddExpResult2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL) 
		return;

	PB_AddExpResultHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer * player = uin2Player(msg.uin());
	if (player)
	{
		int op = msg.op();
		int result = msg.result();

		// 返回星星币添加/扣除结果
		if(MINIW::ScriptVM::game())
		{
			MINIW::ScriptVM::game()->callFunction("AddExpResult", "ii", op, result);
		}			
	}
}

void MpGameSurviveNetHandler::handleRoomExtra2Client(const PB_PACKDATA_CLIENT& pkg)
{
	if (m_root->getCurLoadingState() == CLOAD_CHECK_INFO)
	{
		PB_RoomExtraInfoHC info;
		if (info.ParseFromArray(pkg.MsgData, pkg.ByteSize))
		{
			if (info.room_extra().size() > 0)
			{
				jsonxx::Object extradataObj;
				if (extradataObj.parse(info.room_extra()))
				{
					MNSandbox::GetGlobalEvent().Emit<const std::string>("RoomManager_setCloudServerRoomExtraData", info.room_extra());
				}
			}
			ClientGameManager::getInstance()->setCMURL(info.cmurl(), info.mapmd5(), info.mapid());
		}

		m_root->setCurLadingState(CLOAD_INIT_VM);
	}
}


// 20210824:主机通知客机交换道具结果 codeby：wangyu
void MpGameSurviveNetHandler::handleExchangeItemsToBackPackResult2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL)
		return;

	PB_ExchangeItemsToBackPackResultHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int result = msg.result();
	int type = msg.opertype();
	// 返回道具交换结果
	if (MINIW::ScriptVM::game())
	{
		MINIW::ScriptVM::game()->callFunction("doExchangeItemResult", "ii", type, result);
	}
}

void MpGameSurviveNetHandler::handleBattlePassEvent2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_BPEventHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (MINIW::ScriptVM::game())
	{
		MINIW::ScriptVM::game()->callFunction("NewBattlePassEventOnTrigger", "sis", msg.stype().c_str(), msg.val(), msg.extenddata().c_str());
	}
}

void MpGameSurviveNetHandler::handleHorseFlag2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_HorseFlagHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientActor *actor = objId2ActorOnClient(msg.objid());
	if (!actor)
		return;

	ActorHorse* horse = dynamic_cast<ActorHorse*>(actor);
	if (!horse)
		return;
	
	horse->setHorseFlag(msg.flag());
}

//916冒险 2021/08/18 codeby:wudeshen
void MpGameSurviveNetHandler::handlePlayerOpenUI2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PlayerOpenUIHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	MINIW::ScriptVM::game()->callFunction("CppOpenUI", "ss", msg.uiname().c_str(), msg.uiparam().c_str());
}



void MpGameSurviveNetHandler::handleSetTiangou2Client(const PB_PACKDATA_CLIENT &pkg)
{
	WorldManager *worldMgr = m_root->getWorldMgr();
	PB_SetTiangouHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (g_pPlayerCtrl)
	{
		g_pPlayerCtrl->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, msg.strid());
		if (worldMgr && worldMgr->getWorld(0) && worldMgr->getWorld(0)->getRender() && worldMgr->getWorld(0)->getRender()->getSky())
		{
			worldMgr->getWorld(0)->SetDefaultControlMoonPhase(msg.defaultcontrolmoon());
			worldMgr->getWorld(0)->getRender()->getSky()->setMoonPhase(msg.moonphase());
			if (msg.moonres() != "")
			{
				//todo check
				//worldMgr->getWorld(0)->getRender()->getSky()->SetMoonPhaseRes(msg.moonres());
			}
		}
		MINIW::ScriptVM::game()->callFunction("HandleTiangouUpdate", "b", msg.tiangoustart());
	}
}

//20210827: QQmusic  codeby:wangshuai
void MpGameSurviveNetHandler::handleChangeQQMusicPlayer2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ChangeQQMusicPlayerHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("PB_ChangeQQMusicPlayerHC", SandboxContext(nullptr).SetData_UserObject<PB_ChangeQQMusicPlayerHC>("msg", msg));
}

// 20210910：坐骑隐身技能  codeby： keguanqiang
void MpGameSurviveNetHandler::handleRideInvisible2Client(const PB_PACKDATA_CLIENT &pkg)
{
	if (!m_root) return;
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (!playerCtrl)
		return;

	PB_RideInvisibleHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	long long rideObjId = 0;	// 20210914: 骑乘的坐骑objid codeby： keguanqiang
	for (int i = 0; i < msg.objidlist_size(); i++)
	{
		bool ignoreNameDispobj = false;
		ClientActor *actor = playerCtrl->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(msg.objidlist(i));
		if (i == 0)				// 20210914: 第一个是骑乘的坐骑objid codeby： keguanqiang
		{
			ignoreNameDispobj = true;	// 20210917: 坐骑名字的显示不受隐身技能的影响 codeby： keguanqiang
			rideObjId = msg.objidlist(i);
		}

		if (actor && actor->getBody())
		{
			// 20210914: 隐身后骑乘者能看到特效，其余人看不到特效 begin codeby： keguanqiang
			bool igoreMotion = false;
			auto playerRidComp = playerCtrl->getRiddenComponent();
			if (playerRidComp && playerRidComp->getRidingActorObjId() == rideObjId)
				igoreMotion = true;

			actor->getBody()->show(!msg.invisible(), ignoreNameDispobj, igoreMotion, false);
			// 20210914: 隐身后骑乘者能看到特效，其余人看不到特效 end codeby： keguanqiang		
			ActorLiving *living = dynamic_cast<ActorLiving *>(actor);
			if (living)
			{
				living->setHPProgressDirty();
			}
		}
	}
}
//void MpGameSurviveNetHandler::handleHomelandLevelUpdate2Client(const PB_PACKDATA_CLIENT &pkg)
//{
//	PB_HomeLandLevelUpdateHC msg;
//	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
//
//	for (int i = 0; i < msg.homelevellist().size(); i++)
//	{
//		auto data = msg.homelevellist().Get(i);
//		ClientPlayer *player = uin2Player(data.uin());
//		if (player != NULL && g_pPlayerCtrl->getUin() != data.uin())
//		{
//			player->setHomeLandLevel(data.level());
//			player->updateHomeLandLevelShow(data.level());
//		}
//	}
//}

void MpGameSurviveNetHandler::handlePrayInfoClient(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PrayTreeInfoHC prayTreeInfoHC;
	prayTreeInfoHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_PrayInfoClient", SandboxContext(nullptr).SetData_UserObject<PB_PrayTreeInfoHC>("prayTreeInfoHC", prayTreeInfoHC));
}
void MpGameSurviveNetHandler::handlePrayTreeStageClient(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PrayTreeStageHC prayTreeStageHC;
	prayTreeStageHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_PrayTreeStageClient", SandboxContext(nullptr).SetData_UserObject<PB_PrayTreeStageHC>("prayTreeStageHC", prayTreeStageHC));
}
void MpGameSurviveNetHandler::handlePrayTimeClient(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PrayTreeTimeUpdateHC prayTreeTimeUpdateHC;
	prayTreeTimeUpdateHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (MINIW::ScriptVM::game())
		MINIW::ScriptVM::game()->callFunction("ShowPrayTimeTip", "is", prayTreeTimeUpdateHC.stage(), prayTreeTimeUpdateHC.treetime().c_str());

}
void MpGameSurviveNetHandler::handlePrayReqClient(const PB_PACKDATA_CLIENT &pkg)
{
	PB_PrayTreeReqHC prayTreeReqHC;
	prayTreeReqHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (MINIW::ScriptVM::game())
		MINIW::ScriptVM::game()->callFunction("NotifyReqPray", "si", prayTreeReqHC.treeid().c_str(), prayTreeReqHC.uin());
}


void MpGameSurviveNetHandler::handlePrayTimeHost(int uin, const PB_PACKDATA &pkg)
{
	PB_PrayTreeTimeCH prayTreeTimeCH;
	prayTreeTimeCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (MINIW::ScriptVM::game())
	{
		if (prayTreeTimeCH.stage() == 2)
		{
			MINIW::ScriptVM::game()->callFunction("NotifyFinishTime", "i", prayTreeTimeCH.uin());
		}
		else
		{
			MINIW::ScriptVM::game()->callFunction("NotifyRipeTime", "i", prayTreeTimeCH.uin());
		}
	}
}

void MpGameSurviveNetHandler::handleOpenHomeCloset2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_Open_HomeCloset_HC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	MINIW::ScriptVM::game()->callFunction("OpenHomeCloset", "ss", msg.skinids().c_str(), msg.skinpartids().c_str());
}
void MpGameSurviveNetHandler::handleHomelandRanchInfo2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_HomelandRanchInfoHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_RanchInfo2Client", SandboxContext(nullptr).SetData_UserObject<PB_HomelandRanchInfoHC>("ranchInfoHC", msg));
}
void MpGameSurviveNetHandler::handleHomelandRanchFooderStateClient(const PB_PACKDATA_CLIENT& pkg)
{
	PB_HomeLandRanchFooderStateHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_RanchFooderStateClient", SandboxContext(nullptr).SetData_UserObject<PB_HomeLandRanchFooderStateHC>("ranchFooderState", msg));
}
void MpGameSurviveNetHandler::handleUseItemByHomeLand2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_UseItemByHomelandHC useItemByHomelandHC;
	useItemByHomelandHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (MINIW::ScriptVM::game() && g_pPlayerCtrl)
		MINIW::ScriptVM::game()->callFunction("UseItemByHomeLand", "u[ClientPlayer]ii", g_pPlayerCtrl, useItemByHomelandHC.itemid(), useItemByHomelandHC.itemnum());
}

//20210926: miniClubMusic  codeby:wangshuai
void MpGameSurviveNetHandler::handleMiniClubPlayer2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_MiniClubMusicPlayerHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("PB_MiniClubMusicPlayerHC", SandboxContext(nullptr).SetData_UserObject<PB_MiniClubMusicPlayerHC>("msg", msg));
}

//20211020 客机收到同步增加喷漆数据的处理 codeby:柯冠强
void MpGameSurviveNetHandler::handleAddPaintedInfo2Client(const PB_PACKDATA_CLIENT &pkg)
{
	if (g_WorldMgr == NULL) return;//防止崩溃 code_by:huangfubin 2022.5.27
	PB_AddPaintedInfoHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	SprayPaintMgr * sprayPaintMgr = dynamic_cast<SprayPaintMgr*>(g_WorldMgr->getSandboxMgr("SprayPaintMgr"));
	if (sprayPaintMgr)
	{
		sprayPaintMgr->addPaintedInfoByHostSync(msg);
	}
	//SprayPaintMgr::getInstance()->addPaintedInfoByHostSync(msg);
}

//20211020 客机收到同步删除喷漆数据的处理 codeby:柯冠强
void MpGameSurviveNetHandler::handleRemovePaintedInfo2Client(const PB_PACKDATA_CLIENT &pkg)
{
	if (g_WorldMgr == NULL) return;//防止崩溃 code_by:huangfubin 2022.5.27
	PB_RemovePaintedInfoHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	SprayPaintMgr * sprayPaintMgr = dynamic_cast<SprayPaintMgr*>(g_WorldMgr->getSandboxMgr("SprayPaintMgr"));
	if (sprayPaintMgr)
	{
		sprayPaintMgr->removePaintedInfoByHostSync(msg);
	}
	//SprayPaintMgr::getInstance()->removePaintedInfoByHostSync(msg);
}

//liusijia
void MpGameSurviveNetHandler::handleAdShopBuy2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (!playerCtrl)
	{
		return;
	}

	PB_BuyAdShopGoods buyGoods;
	buyGoods.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (!buyGoods.has_success())
		return;

	int tabId = buyGoods.tabid();
	int goodId = buyGoods.goodid();
	bool success = buyGoods.success();

	if (MINIW::ScriptVM::game())
	{
		MINIW::ScriptVM::game()->callFunction("OnBuyAdShopResult", "iii", tabId, goodId, success);
	}
}

void MpGameSurviveNetHandler::handleSyncPlayerPos2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (!playerCtrl)
	{
		return;
	}

	PB_ResetPosResponeCH msg;
	msg.set_tick(m_root? m_root->getGameTick(): 0);
	GetGameNetManagerPtr()->sendToHost(PB_SYNC_PLAYER_POS_CH, msg);
	
	PB_SyncPlayerPositionHC syncPos;
	syncPos.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	WCoord pos = MPVEC2WCoord(syncPos.position());
	playerCtrl->gotoPos(playerCtrl->getWorld(), pos);
	if (syncPos.has_motion())
	{
		auto &motion = syncPos.motion();
		playerCtrl->setMotionChange(Rainbow::Vector3f(motion.x(), motion.y(), motion.z()));
	}
}
//20211101 手持物品 codeby:luoshuai
void MpGameSurviveNetHandler::handlePlayerEquipWeapon2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_EquipWeaponHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	//MusicClubSync::getSingleton().HandleEquipWeaponMsgForClient(msg);
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("PB_EquipWeaponHC", SandboxContext(nullptr).SetData_UserObject<PB_EquipWeaponHC>("msg", msg));
}
//20210915 音乐方块 codeby:huangxin
void MpGameSurviveNetHandler::handleChangeQQMusicClub2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_ChangeQQMusicClubHC  msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	//MusicClubSync::getSingleton().HandleEquipWeaponMsgForClient(msg);
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("PB_ChangeQQMusicClubHC", SandboxContext(nullptr).SetData_UserObject<PB_ChangeQQMusicClubHC>("msg", msg));

}

void MpGameSurviveNetHandler::handleShowTopBrand2Client(const PB_PACKDATA_CLIENT &pkg)
{
	PB_TopBrandHC  topBrandHC;
	topBrandHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientPlayer *player = uin2Player(topBrandHC.targetuin());
	if (player != nullptr)
	{
		player->setTopBrand(topBrandHC.brandname().data());
	}

}

//20220705 武器熟练度主客机同步 codeby:汪宇
void MpGameSurviveNetHandler::handleWeaponPoint2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_WeaponPointHC pointHC;
	pointHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer* player = uin2Player(pointHC.targetuin());
	if (player != nullptr)
	{
		player->addWeaponSkilledPoint(pointHC.pointtype(), pointHC.itemid());
	}
}

void MpGameSurviveNetHandler::handleAddLightningChain2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_AddLightningChainHC addLightningChainHC;
	addLightningChainHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientActor* actor = objId2ActorOnClient(addLightningChainHC.targetwid());
	long long linkSrcId = addLightningChainHC.linksrcwid();
	ActorLiving* living = dynamic_cast<ActorLiving*>(actor);
	if (!living)
	{
		return;
	}
	LightningChainComponent::ChainType type = LightningChainComponent::ChainType::RUNE;
	if (addLightningChainHC.has_chaintype())
	{
		type = (LightningChainComponent::ChainType)addLightningChainHC.chaintype();
	}
	WCoord startPos{ 0,0,0 };
	if (addLightningChainHC.has_startpos())
	{
		startPos = MPVEC2WCoord(addLightningChainHC.startpos());
	}
	auto* pCom = living->getLightningChainComponent();
	if (pCom)
	{
		if (LightningChainComponent::ChainType::RUNE == type)
		{
			pCom->addChain(linkSrcId, 0, 0, 0, 0, type, {}, startPos);
		}
		else
		{
			pCom->addChain(linkSrcId, 0, 0, LightningChainComponent::ChainType::JELLY);
		}
	}
}

void MpGameSurviveNetHandler::handleStartFishing2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_STARTFISHINGHC pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	WCoord targetPos = MPVEC2WCoord(pbHC.targetpos());
	ClientPlayer* player = uin2Player((int)pbHC.playerid());
	if (!player)
	{
		return;
	}
	auto* pCom = player->getFishingComponent();
	if (pCom)
	{
		pCom->setFishingTarget(targetPos);
		pCom->startFishing();
		pCom->setHookIdFromHost(pbHC.hookid());
	}
}

void MpGameSurviveNetHandler::handleEndFishing2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_ENDFISHINGHC pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientPlayer* player = uin2Player((int)pbHC.playerid());
	if (!player)
	{
		return;
	}
	int resultId = pbHC.resultid();
	auto* pCom = player->getFishingComponent();
	if (pCom)
	{
		pCom->endFishing();
		pCom->setResultFromHost(resultId);
	}
}

void MpGameSurviveNetHandler::handleQuitFishing2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_QUITFISHINGHC pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientPlayer* player = uin2Player((int)pbHC.playerid());
	if (!player)
	{
		return;
	}
	auto* pCom = player->m_pFishingComponent;
	if (pCom)
	{
		pCom->forceQuitFishing();
	}
}

void MpGameSurviveNetHandler::handleChangeFishingState2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_CHANGEFISHINGSTAGEHC pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientPlayer* player = uin2Player((int)pbHC.playerid());
	if (!player)
	{
		return;
	}
	auto* pCom = player->getFishingComponent();
	if (pCom)
	{
		pCom->changeStateFromHost(pbHC.state());
	}
}

void MpGameSurviveNetHandler::handleFishingBeginFlash2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_FishingBeginFlashHC pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientPlayer* player = uin2Player(pbHC.playeruin());
	if (!player)
	{
		return;
	}
	auto* pCom = player->getFishingComponent();
	if (pCom)
	{
		pCom->beginFlash();
	}
}

void MpGameSurviveNetHandler::handlePlayerVehicleMoveInput2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_PlayerVehicleMoveInputHC pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	WorldManager* worldMgr = m_root->getWorldMgr();
	auto actor = objId2ActorOnClient(pbHC.vehicleid());
	if (actor == NULL) return;
	auto vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
	if (vehicle)
	{
		VehicleAssembleLocoMotion* vehicleLoc = static_cast<VehicleAssembleLocoMotion*> (vehicle->getLocoMotion());
		if (vehicleLoc)
		{
			vehicleLoc->m_MoveAccel = vehicle->hasFuel() ? pbHC.accel() : 0;
			vehicleLoc->m_MoveBrake = vehicle->hasFuel() ? pbHC.brake() : 0;
			vehicleLoc->m_MoveLeft = vehicle->getSteeringSwitch() ? pbHC.left() : 0;
			vehicleLoc->m_MoveRight = vehicle->getSteeringSwitch() ? pbHC.right() : 0;
		}
	}
	
}
void MpGameSurviveNetHandler::handleBindItemToActor2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_BindItemToActorHC pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientActor* player = objId2ActorOnClient(pbHC.playerid());
	if (!player)
	{
		return;
	}
	auto* pCom = player->getBindActorCom();
	if (!pCom)
	{
		return;
	}
	if (pbHC.isbind())
	{
		pCom->bindItemFromHost(pbHC.bindid(), pbHC.itemid(), pbHC.anchorid());
	}
	else
	{
		pCom->unbindItemFromHost(pbHC.bindid());
	}
}

//20220809 联机房间 地图模式改变通知（创造/冒险互转） codeby:huangrulin
void MpGameSurviveNetHandler::handleGameModeChange2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_GameModeChangeHC gameModeChangeHC;
	gameModeChangeHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WorldManager* worldMgr = m_root->getWorldMgr();

	if (worldMgr)
	{
		int oldGameMode = gameModeChangeHC.oldgamemode();
		int newGameMode = gameModeChangeHC.newgamemode();

		const int curGameMode = worldMgr->getGameMode();

		if (GAME_NET_MP_GAME_CLIENT == GetClientInfoProxy()->getMultiPlayer()
			&& NORMAL_WORLD == worldMgr->getSpecialType()
			&& curGameMode == oldGameMode)
		{

			MINIW::ScriptVM::game()->callFunction("clientWillChangeMpGameMode", "ii", oldGameMode, newGameMode);
			worldMgr->clientToggleMpGameMode(oldGameMode, newGameMode);
			MINIW::ScriptVM::game()->callFunction("clientEndChangeMpGameMode", "ii", oldGameMode, newGameMode);
		}
	}
}

void  MpGameSurviveNetHandler::handlePushSnowBallSizeChange2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_PushSnowBallSizeChangeHC pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientActor* actor = objId2ActorOnClient(pbHC.actorid());
	if (!actor)
	{
		return;
	}
	ActorPushSnowBall* ball = dynamic_cast<ActorPushSnowBall*>(actor);
	if (ball)
	{
		ball->setBallSize(pbHC.size());
	}
}

void  MpGameSurviveNetHandler::handlePlayEffectShader2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL || playerCtrl->getWorld() == NULL) return;

	EffectManager* effectmgr = playerCtrl->getWorld()->getEffectMgr();

	PB_EffectShader pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	string name = pbHC.name(); //现在只有一个预警特效
	float radius = pbHC.radius();
	float duration = pbHC.duration();
	const PB_Vector3& pos = pbHC.pos();

	if(effectmgr)
		effectmgr->GetGameEffectMgr()->playTerrainDecalEffect(Rainbow::Vector3f(pos.x(), pos.y(), pos.z()), radius, duration, false);
}

void MpGameSurviveNetHandler::handlePlayAnimation2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_ActorPlayAnimHC actorPlayAnimHC;
	actorPlayAnimHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientActor* actor = objId2ActorOnClient(actorPlayAnimHC.objid());

	if (actor && actor->getBody())
	{
		ActorBody* body = actor->getBody();
		if (actorPlayAnimHC.preseqid() > -1)
		{
			if (body->getEntity() && body->getEntity()->GetMainModel() && body->getEntity()->GetMainModel()->IsKindOf<Rainbow::ModelLegacy>())
			{
				Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(body->getEntity()->GetMainModel());
				if (legacymodel != nullptr && legacymodel->GetAnimPlayer())
				{
					legacymodel->GetAnimPlayer()->SetAnimPriority(actorPlayAnimHC.preseqid(), actorPlayAnimHC.prelayer());
				}
			}
		}
		body->playAnimCheck(actorPlayAnimHC.seqid(), actorPlayAnimHC.loop(), actorPlayAnimHC.speed(), actorPlayAnimHC.layer());
	}
}

void MpGameSurviveNetHandler::handleSyncMove2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL || playerCtrl->getWorld() == NULL) return;

	game::hc::PB_MoveSyncHC pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (pbHC.has_pos())
	{
		WCoord pos = MPVEC2WCoord(pbHC.pos());
		if (playerCtrl->getLocoMotion())
			playerCtrl->getLocoMotion()->setPosition(pos.x, pos.y, pos.z);
		playerCtrl->setLastSyncPosition(pos);
		playerCtrl->updateClientMoveSyncInterval(true);
		
		PB_ResetPosResponeCH msg;
		msg.set_tick(m_root? m_root->getGameTick(): 0);
		GetGameNetManagerPtr()->sendToHost(PB_SYNC_PLAYER_POS_CH, msg);
	}
	if (pbHC.has_motion())
	{
		auto &motion = pbHC.motion();
		playerCtrl->setMotionChange(Rainbow::Vector3f(motion.x(), motion.y(), motion.z()));
	}
}

void MpGameSurviveNetHandler::handleUIDisplayHorse2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL || playerCtrl->getWorld() == NULL) return;
	ClientActorMgr* actormgr = playerCtrl->getWorld()->getActorMgr()->ToCastMgr();
	game::hc::PB_UIDisplayHorseHC pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer* player = dynamic_cast<ClientPlayer*>(actormgr->findActorByWID(pbHC.playerobjid()));
	if (player)
	{
		player->setCurAccountHorse(pbHC.horseobjid());
	}
}

void  MpGameSurviveNetHandler::handleResetRoleFlags2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL || playerCtrl->getWorld() == NULL) return;


	PB_ResetRoleFlagsHC pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	unsigned flags = pbHC.flags();
	unsigned types = pbHC.has_types()? pbHC.types(): 0xffffffffu;

	for (size_t i=0; i < sizeof(types) * 8; ++i)
	{
		if (types & (1 << i))
		{
			if (i == ACTORFLAG_FLY)
			{
				playerCtrl->setFlying(flags & (1 << i));
			}
			else
				playerCtrl->setFlagBit(i, flags & (1 << i));
		}
	}
}

bool MpGameSurviveNetHandler::handleBindPlayerToPhysicsPlat2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_BindPlayerToPhysicsPlat pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int uin = pbHC.uin();
	long long objId = pbHC.objid();
	auto localPos = pbHC.localpos();

	ClientActor* actor = objId2ActorOnClient(objId);
	ClientPlayer* player = uin2Player(uin);
	if (actor && player && !player->hasUIControl())
	{
		auto pPhysicCom = actor->getPhysicsComponent();
		if (pPhysicCom)
		{
			player->SetOnPlatform(actor, true);
			Vector3f v3(localPos.x(), localPos.y(), localPos.z());
			// 第二个参数 true 表示来自服务器消息
			pPhysicCom->BindPlayerToPlat(player, true, &v3);
		}
	}
	
	return true;
}

void MpGameSurviveNetHandler::handleBindPlayerToPhysicsPlat2Host(int uinArg, const PB_PACKDATA& pkg)
{
	PB_BindPlayerToPhysicsPlat pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int uin = pbHC.uin();
	long long objId = pbHC.objid();
	auto localPos = pbHC.localpos();

	ClientActor* actor = objId2ActorOnClient(objId);
	ClientPlayer* player = uin2Player(uin);
	if (actor && player && !player->hasUIControl())
	{
		auto pPhysicCom = actor->getPhysicsComponent();
		if (pPhysicCom)
		{
			player->SetOnPlatform(actor, true);
			Vector3f v3(localPos.x(), localPos.y(), localPos.z());
			// 第二个参数 true 表示来自服务器消息
			pPhysicCom->BindPlayerToPlat(player, true, &v3);
		}
	}
}

bool MpGameSurviveNetHandler::handleUnBindPlayerToPhysicsPlat2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_UnBindPlayerToPhysicsPlat pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	long long objId = pbHC.objid();
	ClientActor* actor = objId2ActorOnClient(objId);
	if (actor)
	{
		auto pPhysicCom = actor->getPhysicsComponent();
		if (pPhysicCom)
		{
			for (size_t i = 0; i < pbHC.uin_size(); i++) {
				int uin = pbHC.uin(i);
				ClientPlayer* player = uin2Player(uin);
				if (player && !player->hasUIControl() && player->GetPlatform() == actor)
				{
					player->SetOnPlatform(nullptr, true);
				}
				// 可能在等待player创建再绑定， 也需要清理一下
				pPhysicCom->RemoveWaitBindPlayers(uin);
			}
			pPhysicCom->UnBindPlayer();
		}
	}
	return true;
}

void MpGameSurviveNetHandler::handleUnBindPlayerToPhysicsPlat2Host(int uinArg, const PB_PACKDATA& pkg)
{
	PB_UnBindPlayerToPhysicsPlat pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	long long objId = pbHC.objid();
	ClientActor* actor = objId2ActorOnClient(objId);
	if (actor)
	{
		auto pPhysicCom = actor->getPhysicsComponent();
		if (pPhysicCom)
		{
			for (size_t i = 0; i < pbHC.uin_size(); i++) {
				int uin = pbHC.uin(i);
				ClientPlayer* player = uin2Player(uin);
				if (player && !player->hasUIControl() && player->GetPlatform() == actor)
				{
					player->SetOnPlatform(nullptr, true);
				}
				// 可能在等待player创建再绑定， 也需要清理一下
				pPhysicCom->RemoveWaitBindPlayers(uin);
			}
			pPhysicCom->UnBindPlayer();
		}
	}
}

ClientActor* MpGameSurviveNetHandler::getActorAndInitData(long long objId, long long objIdRoot)
{
	ClientActor* actor = objId2ActorOnClient(objId);
	if (actor)
	{
		if (!actor->IsDelayInited())
		{
			actor->DelayInitDataRecursively();
		}
	}
	else
	{
		// 根节点可能还没有调用delayinit
		if (objIdRoot > 0)
		{
			ClientActor* actorRoot = objId2ActorOnClient(objIdRoot);
			if (actorRoot)
			{
				if (!actorRoot->IsDelayInited())
				{
					actorRoot->DelayInitDataRecursively();
				}
				// 再找一下
				actor = objId2ActorOnClient(objId);
			}
		}
	}
	return actor;
}

bool MpGameSurviveNetHandler::handlePhysicsComUpdate2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_ActorPhysicsComUpdate pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	long long objId = pbHC.objid();
	long long objIdRoot = 0;
	if (pbHC.has_objidroot())
	{
		objIdRoot = pbHC.objidroot();
	}
	auto actor = getActorAndInitData(objId, objIdRoot);
	if (actor && actor->IsDelayInited())
	{
		auto pPhysicCom = actor->getPhysicsComponent();
		if (pPhysicCom) {
			pPhysicCom->OnSyncProp(&pbHC);
		}
	}
	return true;
}

bool MpGameSurviveNetHandler::handlePhysicsComPlatLocPos2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_ActorMoveV2HC actorMoveHC;
	actorMoveHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	long long ObjId = actorMoveHC.objid();
	ObjId = ClientActor::UnpackObjId(ObjId);
	ClientActor* actor = objId2ActorOnClient(ObjId);
	ClientPlayer* pPlayer = dynamic_cast<ClientPlayer*>(actor);
	if (pPlayer)
	{
		auto pPlat = pPlayer->GetPlatform();
		if (pPlat)
		{
			auto pPlayerTransfrom = pPlayer->GetTransform();
			auto pPlatTransfrom = pPlat->GetTransform();
			if (pPlatTransfrom && pPlayerTransfrom->GetParent() == pPlatTransfrom)
			{
				if (actorMoveHC.position_size() == 3)
				{
					WCoord cur_pos;
					cur_pos.x = actorMoveHC.position(0);
					cur_pos.y = actorMoveHC.position(1);
					cur_pos.z = actorMoveHC.position(2);
					pPlayerTransfrom->SetLocalPosition(Vector3f(cur_pos.x, cur_pos.y, cur_pos.z));
				}
				if (actorMoveHC.has_yaw_pitch() && pPlayer->getLocoMotion())
				{
					uint32_t yaw_pitch = actorMoveHC.yaw_pitch();
					float yaw = AngleChar2Float((yaw_pitch & 0xff00) >> 8);
					float pitch = AngleChar2Float(yaw_pitch & 0xff);
					pPlayer->getLocoMotion()->m_RotateYaw = yaw;
					pPlayer->getLocoMotion()->m_RotationPitch = pitch;
				}
			}
		}
	}
	return true;
}

bool MpGameSurviveNetHandler::handleEffctComUpdate2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_EffectComParticleUpd pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	long long objId = pbHC.objid();
	long long objIdRoot = 0;
	if (pbHC.has_objidroot())
	{
		objIdRoot = pbHC.objidroot();
	}
	auto actor = getActorAndInitData(objId, objIdRoot);
	if (actor)
	{
		auto pEffectCom = actor->getEffectComponent();
		if (pEffectCom && pbHC.has_info()) {
			pEffectCom->OnSyncProp(&pbHC.info());
		}
	}
	return true;
}

bool MpGameSurviveNetHandler::handleSoundComUpdate2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_SoundComUpd pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	long long objId = pbHC.objid();
	ClientActor* actor = objId2ActorOnClient(objId);
	if (actor)
	{
		auto pSoundCom = actor->getSoundComponent();
		if (pSoundCom && pbHC.has_info()) {
			pSoundCom->OnSyncProp(&pbHC.info());
		}
	}
	return true;
}

void MpGameSurviveNetHandler::handleMeteorShower2Client(const PB_PACKDATA_CLIENT& pkg)
{
	if (g_WorldMgr == NULL) return;
	PB_MeteorShowerInfo pbInfo;
	pbInfo.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int type = 0;
	if (pbInfo.has_type())
	{
		type = pbInfo.type();
	}

	// 流星雨显示，客机上报
	if (type == 1)
	{
		MINIW::ScriptVM::game()->callFunction("FlowersActivity_GameEventReport", "sii", "meet_meteor", 0, 0);
	}
	// 客机拾取许愿星上报
	else if (type == 2)
	{
		MINIW::ScriptVM::game()->callFunction("FlowersActivity_GameEventReport", "sii", "meet_wishstar", 0, 0);
	}
}

bool MpGameSurviveNetHandler::handleObjActorMsg2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_GeneralEnterAOIHC actorEnterAOIHC;
	actorEnterAOIHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	long long objId = actorEnterAOIHC.objid();
	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL)
	{
		return false;
	}
	if (objId == playerCtrl->getObjId())
	{
		return false;
	}

	ClientActor* actor = objId2ActorOnClient(objId);
	if (!actor)
	{
		return false;
	}
	
	if (actor->getObjType() != OBJ_TYPE_GAMEOBJECT)
	{
		return false;
	}
	const PB_ActorObj& actorObj = actorEnterAOIHC.actorobj();
	actor->LoadFromPBActorObjSimple(&actorObj);
	return true;
}

void MpGameSurviveNetHandler::handleAddBullethole2Client(const PB_PACKDATA_CLIENT &pkg)
{
	if (g_WorldMgr == NULL) return;
	PB_AddBulletholeInfoHC msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	BulletMgr* bulletMgr = dynamic_cast<BulletMgr*>(g_WorldMgr->getSandboxMgr("BulletMgr"));
	if (bulletMgr)
	{
		bulletMgr->addBulletholeInfoByHostSync(msg);
	}
}

void MpGameSurviveNetHandler::handlePlayerCanFire2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_PlayerCanFireHC pbHC;
	pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (g_pPlayerCtrl)
		g_pPlayerCtrl->setActionAttrState(ENABLE_FORBIDFIRE, !pbHC.fire());
}

void MpGameSurviveNetHandler::handlePlayerCustom2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PB_PlayerCustomHC protoHC;
	protoHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	g_pPlayerCtrl->OnPlayerCustomMessage(protoHC.type(), protoHC.data());
}

void MpGameSurviveNetHandler::handleDecomposition2Client(const PB_PACKDATA_CLIENT& pkg)
{
	if (g_WorldMgr == NULL) return;
	World* world = g_WorldMgr->getWorld(0);
	PB_DecompositionHC hc;
	hc.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WCoord blockpos(hc.x(), hc.y(), hc.z());
	if (world->getContainerMgr())
	{
		WorldContainer* container = world->getContainerMgr()->getContainer(blockpos);
		ContainerDecomposition* Decomposition = dynamic_cast<ContainerDecomposition*>(container);
		if (Decomposition)
		{
			Decomposition->OnMessage(hc.type(), hc.data());
		}
	}
}

void MpGameSurviveNetHandler::handleAllSingleBuildData2Client(const PB_PACKDATA_CLIENT& pkg)
{
	World* world = g_WorldMgr->getWorld(0);
	PB_AllSingleBuildDataHC hc;
	hc.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	for (int i = 0; i < hc.builddatas_size(); i++)
	{
		const PB_SingleBuildData& item = hc.builddatas(i);
		const std::string buildName = item.buildname();
		for (int j = 0; j < item.builditem_size(); j++)
		{
			const PB_SingleBuildItemData& subitem = item.builditem(j);
			world->getCityMgr()->onAddSingleBuild(buildName.c_str(), subitem.x(), subitem.z()
				, subitem.crangex(), subitem.crangez(), subitem.brangex(), subitem.brangez(), subitem.issafezone());
		}
	}


	/*jsonxx::Array json_arr;
	for (int i = 0; i < hc.builddatas_size(); i++)
	{
		jsonxx::Object item_obj;
		const PB_SingleBuildData& item = hc.builddatas(i);
		if (item.builditem_size() < 10)
			continue;

		item_obj << "buildName" << item.buildname();

		jsonxx::Array buildarr;
		for (int j = 0; j < item.builditem_size(); j++)
		{
			jsonxx::Object builddata;
			const PB_SingleBuildItemData& subitem = item.builditem(j);

			int rx = subitem.crangex() * CHUNK_BLOCK_X;
			int rz = subitem.crangez() * CHUNK_BLOCK_Z;
			if (subitem.brangex() && subitem.brangez())
			{
				rx = subitem.brangex();
				rz = subitem.brangez();
			}
			WCoord posbegin(subitem.x() * CHUNK_BLOCK_X, 0, subitem.z() * CHUNK_BLOCK_Z);
			WCoord posend = posbegin + WCoord(rx, 0, rz);

			builddata << "x" << (posbegin.x + posend.x) * 0.5;
			builddata << "z" << (posbegin.z + posend.z) * 0.5;
			buildarr.import(builddata);
		}

		item_obj << "builddata" << buildarr;
		json_arr.import(item_obj);
	}
	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("AllSingleBuildData",
		MNSandbox::SandboxContext(nullptr)
		.SetData_String("data", json_arr.json())
	);*/
}
